﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;

namespace ProjetoIntegrador.Professor.P_Disponibilidade
{
    public partial class P_VisualizarDisponibilidade : Form
    {

        private List<Label> labelsDia = new List<Label>();
        private List<Label> labelsInicio = new List<Label>();
        private List<Label> labelsFim = new List<Label>();
        private DataTable dadosDisponibilidades;
        private int paginaAtual = 0;
        private int totalPaginas = 0;
        private int registrosPorPagina = 5;
        private List<TextBox> textBoxesEdicao = new List<TextBox>();
        private bool modoEdicao = false;
        private List<System.Windows.Forms.CheckBox> checkBoxes = new List<System.Windows.Forms.CheckBox>();
        private comandosbanco cmdBanco = new comandosbanco();

        public P_VisualizarDisponibilidade()
        {
            InitializeComponent();
            this.Load += P_VisualizarDisponibilidade_Load;
        }

        private void btn_fechar_visualizardisponibilidades_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
            "Deseja realmente fechar?",
            "Confirmação",
            MessageBoxButtons.YesNo,
             MessageBoxIcon.Question
);

            if (resultado == DialogResult.Yes)
            {
                this.Close();
            }
        }

        private void btn_minimizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void P_VisualizarDisponibilidade_Load(object sender, EventArgs e)
        {

            CarregarDisponibilidades();

        }

        private void btn_excluir_disponibilidade_Click(object sender, EventArgs e)
        {
            // Verificar se há checkboxes selecionados
            var checkboxesSelecionados = checkBoxes.Where(cb => cb.Visible && cb.Checked).ToList();

            if (checkboxesSelecionados.Count == 0)
            {
                MessageBox.Show("Selecione pelo menos uma disponibilidade para excluir.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Obter os dados para confirmação
            var descricoes = checkboxesSelecionados
                .Select(cb =>
                {
                    DataRow row = (DataRow)cb.Tag;
                    string dia = row["DIA_SEMANA"].ToString();
                    string inicio = row["HORARIO_INICIO"].ToString();
                    string fim = row["HORARIO_FIM"].ToString();
                    return $"{dia} ({inicio} - {fim})";
                })
                .ToList();

            string mensagemConfirmacao;
            if (descricoes.Count == 1)
            {
                mensagemConfirmacao = $"Deseja realmente excluir a disponibilidade '{descricoes[0]}'?";
            }
            else
            {
                mensagemConfirmacao = $"Deseja realmente excluir {descricoes.Count} disponibilidades selecionadas?\n\n" +
                                      string.Join("\n", descricoes.Take(5)) +
                                      (descricoes.Count > 5 ? "\n..." : "");
            }

            // Confirmação do usuário
            DialogResult resultado = MessageBox.Show(
                mensagemConfirmacao,
                "Confirmar Exclusão",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                int disponExcluidas = 0;
                var erros = new List<string>();

                foreach (var checkbox in checkboxesSelecionados)
                {
                    DataRow row = (DataRow)checkbox.Tag;
                    int idDisponibilidade = Convert.ToInt32(row["ID_DISPONIBILIDADE"]);

                    string descricao = $"{row["DIA_SEMANA"]} ({row["HORARIO_INICIO"]} - {row["HORARIO_FIM"]})";

                    try
                    {
                        cmdBanco.DeletarDisponibilidade(idDisponibilidade);
                        disponExcluidas++;
                    }
                    catch (Exception ex)
                    {
                        erros.Add($"Erro ao excluir '{descricao}': {ex.Message}");
                    }
                }

                // Mensagem final
                if (erros.Count == 0)
                {
                    string mensagemSucesso = disponExcluidas == 1
                        ? "Disponibilidade excluída com sucesso!"
                        : $"{disponExcluidas} disponibilidades excluídas com sucesso!";

                    MessageBox.Show(mensagemSucesso, "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    string mensagemErro = $"{disponExcluidas} disponibilidades excluídas com sucesso.\n\n" +
                                          $"Erros encontrados:\n{string.Join("\n", erros)}";
                    MessageBox.Show(mensagemErro, "Resultado da Exclusão", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Recarrega os dados na tela
                CarregarDisponibilidades();
            }
        }


        private void CarregarDisponibilidades()
        {
            try
            {
                dadosDisponibilidades = cmdBanco.ConsultarDisponibilidades();
                totalPaginas = (int)Math.Ceiling((double)dadosDisponibilidades.Rows.Count / registrosPorPagina);
                if (totalPaginas == 0) totalPaginas = 1;

                ExibirDadosPagina(); // Certifique-se de que esse método também trata disponibilidade
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar disponibilidades: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExibirDadosPagina()
        {
            // Limpa os labels de disponibilidade
            foreach (var label in labelsDia.Concat(labelsInicio).Concat(labelsFim))
            {
                label.Text = "";
                label.Tag = null;
            }

            // Oculta os checkboxes
            foreach (var cb in checkBoxes)
            {
                cb.Visible = false;
                cb.Checked = false;
            }

            if (dadosDisponibilidades == null || dadosDisponibilidades.Rows.Count == 0)
                return;

            int inicioIndice = paginaAtual * registrosPorPagina;
            int fimIndice = Math.Min(inicioIndice + registrosPorPagina, dadosDisponibilidades.Rows.Count);

            for (int i = inicioIndice; i < fimIndice; i++)
            {
                int indiceLabel = i - inicioIndice;
                DataRow row = dadosDisponibilidades.Rows[i];

                // Exibir checkbox
                checkBoxes[indiceLabel].Visible = true;
                checkBoxes[indiceLabel].Tag = row;

                // Preencher labels com dados da disponibilidade
                labelsDia[indiceLabel].Text = row["DIA_SEMANA"].ToString();
                labelsDia[indiceLabel].Tag = row;

                if (TimeSpan.TryParse(row["HORARIO_INICIO"].ToString(), out TimeSpan inicio))
                    labelsInicio[indiceLabel].Text = inicio.ToString(@"hh\:mm");

                if (TimeSpan.TryParse(row["HORARIO_FIM"].ToString(), out TimeSpan fim))
                    labelsFim[indiceLabel].Text = fim.ToString(@"hh\:mm");

                labelsInicio[indiceLabel].Tag = row;
                labelsFim[indiceLabel].Tag = row;
            }

        }


        private void btn_editar_disponibilidade_Click(object sender, EventArgs e)
        {
            modoEdicao = true;
            btn_salvar_disponibilidade.Visible = true;
            MessageBox.Show("Modo de edição ativado. Dê duplo clique nos campos para editá-los.", "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void IniciarEdicao(Label label, string campo)
        {
            if (!modoEdicao || label.Tag == null) return;

            // Criar TextBox para edição
            TextBox txtEdicao = new TextBox();
            txtEdicao.Text = label.Text;
            txtEdicao.Size = label.Size;
            txtEdicao.Location = label.Location;
            txtEdicao.Font = label.Font;
            txtEdicao.Tag = new { Label = label, Campo = campo, Row = label.Tag };

            // Eventos do TextBox
            txtEdicao.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    FinalizarEdicao(txtEdicao);
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    CancelarEdicao(txtEdicao);
                }
            };

            txtEdicao.LostFocus += (s, e) => FinalizarEdicao(txtEdicao);

            // Adicionar TextBox ao painel na mesma posição do label
            TableLayoutPanelCellPosition posicao = pnl_disponibilidade.GetCellPosition(label);
            pnl_disponibilidade.Controls.Add(txtEdicao, posicao.Column, posicao.Row);

            // Ocultar label e focar no TextBox
            label.Visible = false;
            txtEdicao.Focus();
            txtEdicao.SelectAll();

            textBoxesEdicao.Add(txtEdicao);
        }

        private void FinalizarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;
            string campo = tagInfo.Campo;
            DataRow row = tagInfo.Row;

            if (campo == "HORARIO_INICIO" || campo == "HORARIO_FIM")
            {
                // Validar formato de horário HH:mm:ss ou HH:mm
                if (TimeSpan.TryParse(txtEdicao.Text, out TimeSpan horario))
                {
                    row[campo] = horario;
                    label.Text = horario.ToString(@"hh\:mm"); // exibir no formato "HH:mm"
                }
                else
                {
                    MessageBox.Show($"O campo {campo} deve conter um horário válido (ex: 08:30).", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }
            }
            else if (campo == "DIA_SEMANA")
            {
                //valida se o dia é válido
                if (string.IsNullOrWhiteSpace(txtEdicao.Text))
                {
                    MessageBox.Show("O campo Dia da Semana não pode estar vazio.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }

                row[campo] = txtEdicao.Text.Trim();
                label.Text = txtEdicao.Text.Trim();
            }
            else
            {
              
                row[campo] = txtEdicao.Text;
                label.Text = txtEdicao.Text;
            }

            // Remover TextBox e mostrar label
            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }


        private void CancelarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;

            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        
    }
}
