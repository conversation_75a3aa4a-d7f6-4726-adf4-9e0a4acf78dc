﻿namespace ProjetoIntegrador.C_Disciplina
{
    partial class C_IDisciplina
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(C_IDisciplina));
            btn_inserir_disciplina = new Button();
            cb_selecionarcurso_disciplina = new ComboBox();
            cb_quantidadeaulas_disciplina = new ComboBox();
            pnl_header = new Panel();
            btn_voltar_disciplina = new Button();
            btn_minimizar_disciplina = new Button();
            btn_maximizar_disciplina = new Button();
            lbl_titulo = new Label();
            btn_fechar_disciplina = new Button();
            lbl_inserirdisciplina = new Label();
            txt_nomedisciplina_disciplina = new TextBox();
            txt_cargahoraria_disciplina = new TextBox();
            label2 = new Label();
            label3 = new Label();
            pnl_header.SuspendLayout();
            SuspendLayout();
            // 
            // btn_inserir_disciplina
            // 
            btn_inserir_disciplina.BackColor = Color.Transparent;
            btn_inserir_disciplina.BackgroundImage = (Image)resources.GetObject("btn_inserir_disciplina.BackgroundImage");
            btn_inserir_disciplina.BackgroundImageLayout = ImageLayout.Zoom;
            btn_inserir_disciplina.FlatAppearance.BorderSize = 0;
            btn_inserir_disciplina.FlatStyle = FlatStyle.Flat;
            btn_inserir_disciplina.ForeColor = SystemColors.HighlightText;
            btn_inserir_disciplina.Location = new Point(616, 517);
            btn_inserir_disciplina.Name = "btn_inserir_disciplina";
            btn_inserir_disciplina.Size = new Size(125, 33);
            btn_inserir_disciplina.TabIndex = 10;
            btn_inserir_disciplina.Text = "Inserir";
            btn_inserir_disciplina.UseVisualStyleBackColor = false;
            btn_inserir_disciplina.Click += btn_inserir_Click;
            // 
            // cb_selecionarcurso_disciplina
            // 
            cb_selecionarcurso_disciplina.BackColor = Color.White;
            cb_selecionarcurso_disciplina.FormattingEnabled = true;
            cb_selecionarcurso_disciplina.Location = new Point(469, 318);
            cb_selecionarcurso_disciplina.Name = "cb_selecionarcurso_disciplina";
            cb_selecionarcurso_disciplina.Size = new Size(404, 23);
            cb_selecionarcurso_disciplina.TabIndex = 16;
            cb_selecionarcurso_disciplina.SelectedIndexChanged += cb_selecionarcurso_SelectedIndexChanged;
            // 
            // cb_quantidadeaulas_disciplina
            // 
            cb_quantidadeaulas_disciplina.BackColor = Color.White;
            cb_quantidadeaulas_disciplina.FormattingEnabled = true;
            cb_quantidadeaulas_disciplina.Location = new Point(469, 391);
            cb_quantidadeaulas_disciplina.Name = "cb_quantidadeaulas_disciplina";
            cb_quantidadeaulas_disciplina.Size = new Size(404, 23);
            cb_quantidadeaulas_disciplina.TabIndex = 17;
            // 
            // pnl_header
            // 
            pnl_header.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pnl_header.BackColor = Color.MidnightBlue;
            pnl_header.Controls.Add(btn_voltar_disciplina);
            pnl_header.Controls.Add(btn_minimizar_disciplina);
            pnl_header.Controls.Add(btn_maximizar_disciplina);
            pnl_header.Controls.Add(lbl_titulo);
            pnl_header.Controls.Add(btn_fechar_disciplina);
            pnl_header.Location = new Point(0, 0);
            pnl_header.Name = "pnl_header";
            pnl_header.Size = new Size(1280, 63);
            pnl_header.TabIndex = 25;
            // 
            // btn_voltar_disciplina
            // 
            btn_voltar_disciplina.BackColor = Color.MidnightBlue;
            btn_voltar_disciplina.BackgroundImage = (Image)resources.GetObject("btn_voltar_disciplina.BackgroundImage");
            btn_voltar_disciplina.BackgroundImageLayout = ImageLayout.Zoom;
            btn_voltar_disciplina.FlatAppearance.BorderSize = 0;
            btn_voltar_disciplina.FlatStyle = FlatStyle.Flat;
            btn_voltar_disciplina.Location = new Point(35, 18);
            btn_voltar_disciplina.Name = "btn_voltar_disciplina";
            btn_voltar_disciplina.Size = new Size(30, 21);
            btn_voltar_disciplina.TabIndex = 23;
            btn_voltar_disciplina.UseVisualStyleBackColor = false;
            btn_voltar_disciplina.Click += btn_voltar_disciplina_Click_1;
            // 
            // btn_minimizar_disciplina
            // 
            btn_minimizar_disciplina.BackgroundImage = (Image)resources.GetObject("btn_minimizar_disciplina.BackgroundImage");
            btn_minimizar_disciplina.BackgroundImageLayout = ImageLayout.Stretch;
            btn_minimizar_disciplina.FlatAppearance.BorderSize = 0;
            btn_minimizar_disciplina.FlatStyle = FlatStyle.Flat;
            btn_minimizar_disciplina.Location = new Point(1173, 22);
            btn_minimizar_disciplina.Name = "btn_minimizar_disciplina";
            btn_minimizar_disciplina.Size = new Size(21, 19);
            btn_minimizar_disciplina.TabIndex = 22;
            btn_minimizar_disciplina.UseVisualStyleBackColor = true;
            btn_minimizar_disciplina.Click += btn_minimizar_disciplina_Click_1;
            // 
            // btn_maximizar_disciplina
            // 
            btn_maximizar_disciplina.BackgroundImage = (Image)resources.GetObject("btn_maximizar_disciplina.BackgroundImage");
            btn_maximizar_disciplina.BackgroundImageLayout = ImageLayout.Zoom;
            btn_maximizar_disciplina.FlatAppearance.BorderSize = 0;
            btn_maximizar_disciplina.FlatStyle = FlatStyle.Flat;
            btn_maximizar_disciplina.Location = new Point(1209, 18);
            btn_maximizar_disciplina.Name = "btn_maximizar_disciplina";
            btn_maximizar_disciplina.Size = new Size(19, 23);
            btn_maximizar_disciplina.TabIndex = 21;
            btn_maximizar_disciplina.UseVisualStyleBackColor = true;
            btn_maximizar_disciplina.Click += btn_maximizar_disciplina_Click_1;
            // 
            // lbl_titulo
            // 
            lbl_titulo.AutoSize = true;
            lbl_titulo.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_titulo.ForeColor = SystemColors.ButtonHighlight;
            lbl_titulo.Location = new Point(632, 18);
            lbl_titulo.Name = "lbl_titulo";
            lbl_titulo.Size = new Size(62, 25);
            lbl_titulo.TabIndex = 1;
            lbl_titulo.Text = "SEHD";
            // 
            // btn_fechar_disciplina
            // 
            btn_fechar_disciplina.BackColor = Color.MidnightBlue;
            btn_fechar_disciplina.BackgroundImage = (Image)resources.GetObject("btn_fechar_disciplina.BackgroundImage");
            btn_fechar_disciplina.BackgroundImageLayout = ImageLayout.Zoom;
            btn_fechar_disciplina.FlatAppearance.BorderSize = 0;
            btn_fechar_disciplina.FlatStyle = FlatStyle.Flat;
            btn_fechar_disciplina.Location = new Point(1234, 18);
            btn_fechar_disciplina.Name = "btn_fechar_disciplina";
            btn_fechar_disciplina.Size = new Size(34, 21);
            btn_fechar_disciplina.TabIndex = 20;
            btn_fechar_disciplina.UseVisualStyleBackColor = false;
            btn_fechar_disciplina.Click += btn_fechar_disciplina_Click;
            // 
            // lbl_inserirdisciplina
            // 
            lbl_inserirdisciplina.AutoSize = true;
            lbl_inserirdisciplina.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_inserirdisciplina.ForeColor = Color.MidnightBlue;
            lbl_inserirdisciplina.Location = new Point(593, 164);
            lbl_inserirdisciplina.Name = "lbl_inserirdisciplina";
            lbl_inserirdisciplina.Size = new Size(157, 25);
            lbl_inserirdisciplina.TabIndex = 37;
            lbl_inserirdisciplina.Text = "Inserir disciplina";
            // 
            // txt_nomedisciplina_disciplina
            // 
            txt_nomedisciplina_disciplina.Location = new Point(469, 237);
            txt_nomedisciplina_disciplina.Name = "txt_nomedisciplina_disciplina";
            txt_nomedisciplina_disciplina.PlaceholderText = "Nome da disciplina";
            txt_nomedisciplina_disciplina.Size = new Size(404, 23);
            txt_nomedisciplina_disciplina.TabIndex = 38;
            // 
            // txt_cargahoraria_disciplina
            // 
            txt_cargahoraria_disciplina.Location = new Point(469, 452);
            txt_cargahoraria_disciplina.Name = "txt_cargahoraria_disciplina";
            txt_cargahoraria_disciplina.PlaceholderText = "Carga horária";
            txt_cargahoraria_disciplina.Size = new Size(404, 23);
            txt_cargahoraria_disciplina.TabIndex = 39;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new Font("Segoe UI Semibold", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label2.ForeColor = Color.DimGray;
            label2.Location = new Point(469, 300);
            label2.Name = "label2";
            label2.Size = new Size(106, 17);
            label2.TabIndex = 42;
            label2.Text = "Selecionar curso";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("Segoe UI Semibold", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label3.ForeColor = Color.DimGray;
            label3.Location = new Point(469, 371);
            label3.Name = "label3";
            label3.Size = new Size(101, 17);
            label3.TabIndex = 43;
            label3.Text = "Aulas semanais";
            // 
            // C_IDisciplina
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1280, 702);
            Controls.Add(label3);
            Controls.Add(label2);
            Controls.Add(txt_cargahoraria_disciplina);
            Controls.Add(txt_nomedisciplina_disciplina);
            Controls.Add(lbl_inserirdisciplina);
            Controls.Add(pnl_header);
            Controls.Add(cb_quantidadeaulas_disciplina);
            Controls.Add(cb_selecionarcurso_disciplina);
            Controls.Add(btn_inserir_disciplina);
            FormBorderStyle = FormBorderStyle.None;
            Name = "C_IDisciplina";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "C_Disciplina";
            Load += C_IDisciplina_Load;
            pnl_header.ResumeLayout(false);
            pnl_header.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
        private Button btn_inserir_disciplina;
        private ComboBox cb_selecionarcurso_disciplina;
        private ComboBox cb_quantidadeaulas_disciplina;
        private Panel pnl_header;
        private Button btn_voltar_disciplina;
        private Button btn_minimizar_disciplina;
        private Button btn_maximizar_disciplina;
        private Label lbl_titulo;
        private Button btn_fechar_disciplina;
        private Label lbl_inserirdisciplina;
        private TextBox txt_nomedisciplina_disciplina;
        private TextBox txt_cargahoraria_disciplina;
        private Label label2;
        private Label label3;
    }
}