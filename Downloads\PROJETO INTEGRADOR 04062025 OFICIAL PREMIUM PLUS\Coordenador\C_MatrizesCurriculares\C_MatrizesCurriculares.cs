﻿using System;
using System.Windows.Forms;
using System.Data; // Adicionar para DataTable
using System.Collections.Generic; // Adicionar para List
using System.Drawing; // Adicionar para Size e Point
using ProjetoIntegrador.Classes; // Adicionar esta linha
using System.Linq; // Necessário para Concat em ExibirDadosPagina, como em C_VisualizarProfessores

namespace ProjetoIntegrador.C_matrizescurriculares
{
    public partial class C_MatrizesCurriculares : Form
    {
        private comandosbanco cmdBanco = new comandosbanco(); // Assumindo que comandosbanco é acessível
        private DataTable dadosMatrizes;
        private int paginaAtual = 0;
        private int registrosPorPagina = 9; // Exibir 9 linhas de dados, como em C_VisualizarProfessores
        private int totalPaginas = 0;

        // Listas de Labels para exibir os dados (similar ao C_VisualizarProfessores)
        private List<Label> labelsDisciplina = new List<Label>();
        private List<Label> labelsAulasSemanais = new List<Label>();
        private List<Label> labelsHoraAno = new List<Label>();

        public C_MatrizesCurriculares()
        {
            InitializeComponent(); // Inicializa os controles definidos no designer
            InicializarComponentesPersonalizados(); // Configura a exibição de dados e eventos
            PopularComboBoxCursos(); // Popula o ComboBox de cursos e carrega os dados iniciais
        }

        private void InicializarComponentesPersonalizados()
        {
            // Criar labels para exibir os dados dentro do pnl_matriz
            CriarLabelsParaDadosMatrizes();

            // Assinar eventos dos botões de paginação existentes no designer
            btn_anterior_matrizescurriculares.Click += BtnAnterior_Click;
            btn_proximo_matrizescurriculares.Click += BtnProximo_Click;

            // Assinar evento do ComboBox existente no designer
            cb_selecionarcurso_matrizescurriculares.SelectedIndexChanged += cb_selecionarcurso_matrizescurriculares_SelectedIndexChanged;
        }

        private void CriarLabelsParaDadosMatrizes()
        {
            // Limpar controles existentes no pnl_matriz, exceto o cabeçalho (se pnl_matriz for um TableLayoutPanel com cabeçalho no designer)
            if (pnl_matriz is TableLayoutPanel tableLayoutPanel)
            {
                // Remover labels de dados antigos antes de criar novos
                var controlsToRemove = new List<Control>();
                foreach (Control control in tableLayoutPanel.Controls)
                {
                    // Verificar se o controle está em uma linha de dados (não na linha do cabeçalho)
                    TableLayoutPanelCellPosition position = tableLayoutPanel.GetPositionFromControl(control);
                    if (position.Row > 0)
                    {
                        controlsToRemove.Add(control);
                    }
                }

                foreach (Control control in controlsToRemove)
                {
                    tableLayoutPanel.Controls.Remove(control);
                    control.Dispose();
                }

                labelsDisciplina.Clear();
                labelsAulasSemanais.Clear();
                labelsHoraAno.Clear();

                for (int i = 0; i < registrosPorPagina; i++)
                {
                    // Labels para Disciplina
                    Label lblDisciplina = new Label();
                    lblDisciplina.Dock = DockStyle.Fill;
                    lblDisciplina.TextAlign = ContentAlignment.MiddleCenter;
                    lblDisciplina.Padding = new Padding(5);
                    lblDisciplina.AutoSize = false;
                    // Sem DoubleClick event, pois não há edição
                    tableLayoutPanel.Controls.Add(lblDisciplina, 0, i + 1); // Adiciona na primeira coluna, linha i + 1 (após o cabeçalho)
                    labelsDisciplina.Add(lblDisciplina);

                    // Labels para Aulas Semanais
                    Label lblAulasSemanais = new Label();
                    lblAulasSemanais.Dock = DockStyle.Fill;
                    lblAulasSemanais.TextAlign = ContentAlignment.MiddleCenter;
                    lblAulasSemanais.Padding = new Padding(5);
                    lblAulasSemanais.AutoSize = false;
                    // Sem DoubleClick event
                    tableLayoutPanel.Controls.Add(lblAulasSemanais, 1, i + 1); // Adiciona na segunda coluna, linha i + 1
                    labelsAulasSemanais.Add(lblAulasSemanais);

                    // Labels para Hora / Ano
                    Label lblHoraAno = new Label();
                    lblHoraAno.Dock = DockStyle.Fill;
                    lblHoraAno.TextAlign = ContentAlignment.MiddleCenter;
                    lblHoraAno.Padding = new Padding(5);
                    lblHoraAno.AutoSize = false;
                    // Sem DoubleClick event
                    tableLayoutPanel.Controls.Add(lblHoraAno, 2, i + 1); // Adiciona na terceira coluna, linha i + 1
                    labelsHoraAno.Add(lblHoraAno);
                }
            }
            else
            {
                // Tratar caso pnl_matriz não seja um TableLayoutPanel (menos provável dado o layout desejado)
                MessageBox.Show("O painel 'pnl_matriz' não parece ser um TableLayoutPanel. O layout pode não ser exibido corretamente.", "Aviso de Configuração", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                // Se pnl_matriz for um Panel genérico, a lógica de posicionamento dos labels seria diferente
                // Seria necessário calcular as posições manualmente ou usar outro contêiner.
            }
        }

        private void PopularComboBoxCursos()
        {
            try
            {
                DataTable dadosCursos = cmdBanco.ConsultarCursos(); // Assumindo que esta função existe em comandosbanco
                cb_selecionarcurso_matrizescurriculares.DataSource = dadosCursos;
                cb_selecionarcurso_matrizescurriculares.DisplayMember = "NOME_CURSO"; // Coluna a ser exibida
                cb_selecionarcurso_matrizescurriculares.ValueMember = "ID_CURSO"; // Coluna com o valor real (ID)

                // Disparar o evento SelectedIndexChanged para carregar os dados do primeiro curso ao iniciar
                // Isso só deve acontecer se houver itens no ComboBox
                if (cb_selecionarcurso_matrizescurriculares.Items.Count > 0)
                {
                    // Definir SelectedIndex dispara o evento SelectedIndexChanged
                    cb_selecionarcurso_matrizescurriculares.SelectedIndex = 0;
                }
                else
                {
                    // Se não houver cursos, limpar a tabela e paginação
                    if (dadosMatrizes != null) dadosMatrizes.Clear();
                    paginaAtual = 0;
                    totalPaginas = 0;
                    ExibirDadosPagina(); // Atualizar a exibição para mostrar tabela vazia e paginação 0 de 0
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar cursos: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void cb_selecionarcurso_matrizescurriculares_SelectedIndexChanged(object sender, EventArgs e)
        {
            // Verificar se há um curso válido selecionado
            int idCurso = ObterIdCursoSelecionado();
            if (idCurso != -1)
            {
                CarregarMatrizes(); // Recarregar matrizes quando o curso mudar
            }
            else
            {
                // Limpar dados da tabela se nenhum curso estiver selecionado
                if (dadosMatrizes != null) dadosMatrizes.Clear();
                paginaAtual = 0;
                totalPaginas = 0;
                ExibirDadosPagina(); // Atualizar a exibição para mostrar tabela vazia e paginação 0 de 0
            }
        }

        /// <summary>
        /// Método auxiliar para obter o ID do curso selecionado no ComboBox de forma segura
        /// </summary>
        /// <returns>ID do curso selecionado ou -1 se nenhum curso válido estiver selecionado</returns>
        private int ObterIdCursoSelecionado()
        {
            try
            {
                // Verificar se há algo selecionado
                if (cb_selecionarcurso_matrizescurriculares.SelectedIndex == -1 ||
                    cb_selecionarcurso_matrizescurriculares.SelectedValue == null ||
                    cb_selecionarcurso_matrizescurriculares.SelectedValue == DBNull.Value)
                {
                    return -1;
                }

                // Tentar obter o valor usando diferentes abordagens
                object selectedValue = cb_selecionarcurso_matrizescurriculares.SelectedValue;

                // Se o SelectedValue for um DataRowView (comum quando usando DataSource)
                if (selectedValue is DataRowView dataRowView)
                {
                    // Obter o valor da coluna ID_CURSO do DataRowView
                    if (dataRowView.Row.Table.Columns.Contains("ID_CURSO") &&
                        dataRowView["ID_CURSO"] != DBNull.Value)
                    {
                        return Convert.ToInt32(dataRowView["ID_CURSO"]);
                    }
                }
                // Se for um valor direto (int, string, etc.)
                else
                {
                    return Convert.ToInt32(selectedValue);
                }

                return -1;
            }
            catch (Exception ex)
            {
                // Log do erro para debug, mas não mostrar MessageBox aqui para evitar spam
                System.Diagnostics.Debug.WriteLine($"Erro ao obter ID do curso: {ex.Message}");
                return -1;
            }
        }

        private void CarregarMatrizes()
        {
            try
            {
                // Obter o ID do curso selecionado usando método auxiliar
                int idCursoSelecionado = ObterIdCursoSelecionado();

                if (idCursoSelecionado == -1)
                {
                    // Se nenhum curso válido for selecionado, limpar os dados
                    if (dadosMatrizes != null) dadosMatrizes.Clear();
                    else dadosMatrizes = new DataTable(); // Garantir que dadosMatrizes não é null

                    totalPaginas = 1;
                    paginaAtual = 0;
                    ExibirDadosPagina();
                    return;
                }

                // Chamar a função CONSULTAR no comandosbanco para obter dados filtrados
                dadosMatrizes = cmdBanco.ConsultarMatrizesCurriculares(idCursoSelecionado);

                totalPaginas = (int)Math.Ceiling((double)dadosMatrizes.Rows.Count / registrosPorPagina);
                if (totalPaginas == 0) totalPaginas = 1;
                paginaAtual = 0; // Resetar para a primeira página

                ExibirDadosPagina();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar matrizes curriculares: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Em caso de erro, garantir que temos uma estrutura válida
                if (dadosMatrizes == null) dadosMatrizes = new DataTable();
                totalPaginas = 1;
                paginaAtual = 0;
                ExibirDadosPagina();
            }
        }

        private void ExibirDadosPagina()
        {
            // Limpar o texto de todos os labels de dados
            foreach (var label in labelsDisciplina.Concat(labelsAulasSemanais).Concat(labelsHoraAno))
            {
                label.Text = "";
            }

            if (dadosMatrizes == null || dadosMatrizes.Rows.Count == 0)
            {
                lbl_paginacao_matrizescurriculares.Text = "Página 0 de 0";
                btn_anterior_matrizescurriculares.Enabled = false;
                btn_proximo_matrizescurriculares.Enabled = false;
                return;
            }

            int inicioIndice = paginaAtual * registrosPorPagina;
            int fimIndice = Math.Min(inicioIndice + registrosPorPagina, dadosMatrizes.Rows.Count);

            for (int i = inicioIndice; i < fimIndice; i++)
            {
                int indiceLabel = i - inicioIndice;
                DataRow row = dadosMatrizes.Rows[i];

                // Preencher dados nos labels, verificando se as colunas existem
                labelsDisciplina[indiceLabel].Text = row.Table.Columns.Contains("Disciplina") && row["Disciplina"] != DBNull.Value ? row["Disciplina"].ToString() : "";
                labelsAulasSemanais[indiceLabel].Text = row.Table.Columns.Contains("AulasSemanais") && row["AulasSemanais"] != DBNull.Value ? row["AulasSemanais"].ToString() : "";
                labelsHoraAno[indiceLabel].Text = row.Table.Columns.Contains("HoraAno") && row["HoraAno"] != DBNull.Value ? row["HoraAno"].ToString() : "";
            }

            AtualizarControlePaginacao();
        }

        private void AtualizarControlePaginacao()
        {
            lbl_paginacao_matrizescurriculares.Text = $"Página {paginaAtual + 1} de {totalPaginas}";
            btn_anterior_matrizescurriculares.Enabled = paginaAtual > 0;
            btn_proximo_matrizescurriculares.Enabled = paginaAtual < totalPaginas - 1;
        }

        private void BtnAnterior_Click(object sender, EventArgs e)
        {
            if (paginaAtual > 0)
            {
                paginaAtual--;
                ExibirDadosPagina();
            }
        }

        private void BtnProximo_Click(object sender, EventArgs e)
        {
            if (paginaAtual < totalPaginas - 1)
            {
                paginaAtual++;
                ExibirDadosPagina();
            }
        }

        private void MatrizesCurriculares_Load(object sender, EventArgs e)
        {
            // O carregamento inicial agora é feito no construtor chamando PopularComboBoxCursos()
        }

        private void btn_fechar_matrizescurriculares_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto
        }

        private void btn_maximizar_matrizescurriculares_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_minimizar_matrizescurriculares_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void AbrirTela(Form novaTela)
        {
            bool estaMaximizado = this.WindowState == FormWindowState.Maximized;

            this.Hide();
            if (estaMaximizado)
                novaTela.WindowState = FormWindowState.Maximized;

            novaTela.FormClosed += (s, args) =>
            {
                this.Show();
                if (estaMaximizado)
                    this.WindowState = FormWindowState.Maximized;
            };

            novaTela.Show();
        }
        private void btn_voltar_matrizescurriculares_Click(object sender, EventArgs e)
        {
            var telaPrincipal = new ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal();
            AbrirTela(telaPrincipal);
        }

        private void btn_anterior_matrizescurriculares_Click(object sender, EventArgs e)
        {

        }
    }
}
