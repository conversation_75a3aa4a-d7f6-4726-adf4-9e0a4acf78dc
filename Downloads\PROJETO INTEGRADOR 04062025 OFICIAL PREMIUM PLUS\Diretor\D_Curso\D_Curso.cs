﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;

namespace ProjetoIntegrador.Diretor
{
    public partial class D_Curso : Form
    {
        public D_Curso()
        {
            InitializeComponent();
        }


        private void btn_fechar_curso_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
            "Deseja realmente fechar a aplicação?",
            "Confirmação",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }

        private void btn_maximizar_curso_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_inserir_Click(object sender, EventArgs e)
        {
            // Obtém os controles do formulário
            TextBox txtNomeCurso = Controls.Find("txt_nomecurso", true).FirstOrDefault() as TextBox;
            TextBox txtQuantidadeAulas = Controls.Find("txt_quantidadeaulas", true).FirstOrDefault() as TextBox;
            TextBox txtInicioPeriodo = Controls.Find("txt_inicioperiodo", true).FirstOrDefault() as TextBox;
            TextBox txtFimPeriodo = Controls.Find("txt_fimperiodo", true).FirstOrDefault() as TextBox;

            // Valida se os campos estão preenchidos
            if (txtNomeCurso != null && txtQuantidadeAulas != null && txtInicioPeriodo != null && txtFimPeriodo != null &&
                !string.IsNullOrWhiteSpace(txtNomeCurso.Text) &&
                !string.IsNullOrWhiteSpace(txtQuantidadeAulas.Text) &&
                !string.IsNullOrWhiteSpace(txtInicioPeriodo.Text) &&
                !string.IsNullOrWhiteSpace(txtFimPeriodo.Text) &&
                DateOnly.TryParse(txtInicioPeriodo.Text, out DateOnly inicioPeriodo) &&
                DateOnly.TryParse(txtFimPeriodo.Text, out DateOnly fimPeriodo))
            {
                try
                {
                    // Cria o modelo e preenche com dados do formulário
                    ProjetoIntegrador.Classes.variaveisbanco modelo = new ProjetoIntegrador.Classes.variaveisbanco
                    {
                        nomecurso = txtNomeCurso.Text,
                        quantidadeaulas = txtQuantidadeAulas.Text,
                        inicioperiodo = inicioPeriodo,
                        fimperiodo = fimPeriodo
                    };

                    // Instancia a classe de comandos do banco
                    ProjetoIntegrador.Classes.comandosbanco cmdBanco = new ProjetoIntegrador.Classes.comandosbanco();

                    // Cadastra o curso
                    cmdBanco.CadastroCurso(modelo);

                    MessageBox.Show("Curso cadastrado com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Limpa os campos após o cadastro
                    txtNomeCurso.Clear();
                    txtQuantidadeAulas.Clear();
                    txtInicioPeriodo.Clear();
                    txtFimPeriodo.Clear();
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Erro ao cadastrar curso: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("Preencha todos os campos obrigatórios corretamente. As datas devem estar no formato dd/mm/aaaa.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void btn_minimizar_curso_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }
    }
}
