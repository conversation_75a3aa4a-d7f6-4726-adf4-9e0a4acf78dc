﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using ProjetoIntegrador.Classes;

namespace ProjetoIntegrador.Professor
{
    public partial class P_IDisponibilidade : Form
    {

        public P_IDisponibilidade()
        {
            InitializeComponent();
            CarregarDiasSemana();
        }

        // Método para carregar os dias da semana no ComboBox
        private void CarregarDiasSemana()
        {
            try
            {
                // Limpa o ComboBox
                ComboBox cbDias = Controls.Find("cb_selecionardias", true).FirstOrDefault() as ComboBox;

                if (cbDias != null)
                {
                    cbDias.Items.Clear();

                    // Adiciona um item padrão
                    cbDias.Items.Add("Selecione um dia...");

                    // Adiciona os dias da semana
                    cbDias.Items.Add("Segunda-feira");
                    cbDias.Items.Add("Terça-feira");
                    cbDias.Items.Add("Quarta-feira");
                    cbDias.Items.Add("Quinta-feira");
                    cbDias.Items.Add("Sexta-feira");
                    cbDias.Items.Add("Sábado");

                    // Seleciona o item padrão
                    cbDias.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar dias da semana: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_fechar_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto


        }

        private void btn_minimizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_inserir_Click(object sender, EventArgs e)
        {
            if (cb_selecionardias_disponibilidade.SelectedIndex != -1 &&
               TimeSpan.TryParse(txt_horarioinicio_disponibilidade.Text, out TimeSpan horarioInicio) &&
               TimeSpan.TryParse(txt_horariofim_disponibilidade.Text, out TimeSpan horarioFim))
            {
                variaveisbanco modelo = new variaveisbanco
                {
                    diasemana = cb_selecionardias_disponibilidade.Text,
                    horarioinicio = horarioInicio,
                    horariofim = horarioFim
                };

                try
                {
                    comandosbanco cmdBanco = new comandosbanco();
                    cmdBanco.CadastroDisponibilidadeSimplificado(modelo);

                    MessageBox.Show($"Disponibilidade cadastrada com sucesso!\n" +
                                    $"Dia: {modelo.diasemana}\n" +
                                    $"Início: {modelo.horarioinicio:hh\\:mm}\n" +
                                    $"Fim: {modelo.horariofim:hh\\:mm}",
                                    "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    LimparCampos(); // Método que limpa os campos do formulário
                }
                catch (MySqlException sqlEx)
                {
                    string mensagemErro = "Erro no banco de dados: ";
                    if (sqlEx.Message.Contains("Duplicate entry"))
                        mensagemErro += "Já existe uma disponibilidade igual cadastrada.";
                    else
                        mensagemErro += sqlEx.Message;

                    MessageBox.Show(mensagemErro, "Erro de Banco de Dados", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Erro inesperado ao cadastrar disponibilidade:\n{ex.Message}\n\nDetalhes técnicos:\n{ex.StackTrace}",
                                    "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("Preencha todos os campos obrigatórios corretamente.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

        }

        private void LimparCampos()

        {

            txt_horarioinicio_disponibilidade.Text = "";
            txt_horariofim_disponibilidade.Text = "";
            cb_selecionardias_disponibilidade.SelectedIndex = -1;

        }

        private void P_IDisponibilidade_Load(object sender, EventArgs e)
        {

        }

    }
}
