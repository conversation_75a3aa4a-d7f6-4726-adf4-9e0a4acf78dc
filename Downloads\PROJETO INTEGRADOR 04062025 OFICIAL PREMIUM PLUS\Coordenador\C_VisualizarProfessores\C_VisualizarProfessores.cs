﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using ProjetoIntegrador.Classes;
using ProjetoIntegrador.conn;

namespace ProjetoIntegrador.Coordenador.C_VisualizarProfessores
{
    public partial class C_VisualizarProfessores : Form
    {
        private comandosbanco cmdBanco = new comandosbanco();
        private DataTable dadosProfessores;
        private int paginaAtual = 0;
        private int registrosPorPagina = 9;
        private int totalPaginas = 0;
        private bool modoEdicao = false;
        private List<Label> labelsNome = new List<Label>();
        private List<Label> labelsCurso = new List<Label>();
        private List<Label> labelsDisciplina = new List<Label>();
        private List<TextBox> textBoxesEdicao = new List<TextBox>();
        private List<CheckBox> checkBoxes = new List<CheckBox>();

        public C_VisualizarProfessores()
        {
            InitializeComponent();
            InicializarComponentes();
            CarregarProfessores();
        }

        private void InicializarComponentes()
        {
            // Inicializar lista de checkboxes
            checkBoxes.AddRange(new CheckBox[] {
                chb_linha1_visualizarprofessores,
                chb_linha2_visualizarprofessores,
                chb_linha3_visualizarprofessores,
                chb_linha4_visualizarprofessores,
                chb_linha5_visualizarprofessores,
                chb_linha6_visualizarprofessores,
                chb_linha7_visualizarprofessores,
                chb_linha8_visualizarprofessores,
                chb_linha9_visualizarprofessores
            });

            // Criar labels para exibir dados
            CriarLabelsParaDados();
        }

        private void CriarLabelsParaDados()
        {
            for (int i = 0; i < registrosPorPagina; i++)
            {
                // Labels para Nome do Professor
                Label lblNome = new Label();
                lblNome.AutoSize = false;
                lblNome.Size = new Size(280, 40);
                lblNome.TextAlign = ContentAlignment.MiddleLeft;
                lblNome.Padding = new Padding(25, 0, 0, 0);
                lblNome.BackColor = Color.Transparent;
                lblNome.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblNome.DoubleClick += (s, e) => IniciarEdicao(s as Label, "NOME_PROFESSOR");
                pnl_professores.Controls.Add(lblNome, 0, i + 1);
                labelsNome.Add(lblNome);

                // Labels para Curso
                Label lblCurso = new Label();
                lblCurso.AutoSize = false;
                lblCurso.Size = new Size(190, 40);
                lblCurso.TextAlign = ContentAlignment.MiddleCenter;
                lblCurso.BackColor = Color.Transparent;
                lblCurso.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblCurso.DoubleClick += (s, e) => IniciarEdicao(s as Label, "NOME_CURSO");
                pnl_professores.Controls.Add(lblCurso, 1, i + 1);
                labelsCurso.Add(lblCurso);

                // Labels para Disciplina
                Label lblDisciplina = new Label();
                lblDisciplina.AutoSize = false;
                lblDisciplina.Size = new Size(190, 40);
                lblDisciplina.TextAlign = ContentAlignment.MiddleCenter;
                lblDisciplina.BackColor = Color.Transparent;
                lblDisciplina.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblDisciplina.DoubleClick += (s, e) => IniciarEdicao(s as Label, "NOME_DISCIPLINA");
                pnl_professores.Controls.Add(lblDisciplina, 2, i + 1);
                labelsDisciplina.Add(lblDisciplina);
            }
        }

        private void CarregarProfessores()
        {
            try
            {
                dadosProfessores = cmdBanco.ConsultarProfessoresComDisciplinas();
                totalPaginas = (int)Math.Ceiling((double)dadosProfessores.Rows.Count / registrosPorPagina);
                if (totalPaginas == 0) totalPaginas = 1;

                ExibirDadosPagina();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar professores: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExibirDadosPagina()
        {
            // Limpar todos os labels
            foreach (var label in labelsNome.Concat(labelsCurso).Concat(labelsDisciplina))
            {
                label.Text = "";
                label.Tag = null;
            }

            // Ocultar todos os checkboxes
            foreach (var cb in checkBoxes)
            {
                cb.Visible = false;
                cb.Checked = false;
            }

            if (dadosProfessores == null || dadosProfessores.Rows.Count == 0)
                return;

            int inicioIndice = paginaAtual * registrosPorPagina;
            int fimIndice = Math.Min(inicioIndice + registrosPorPagina, dadosProfessores.Rows.Count);

            for (int i = inicioIndice; i < fimIndice; i++)
            {
                int indiceLabel = i - inicioIndice;
                DataRow row = dadosProfessores.Rows[i];

                // Exibir checkbox
                checkBoxes[indiceLabel].Visible = true;
                checkBoxes[indiceLabel].Tag = row;

                // Preencher dados nos labels
                labelsNome[indiceLabel].Text = row["NOME_PROFESSOR"].ToString();
                labelsNome[indiceLabel].Tag = row;

                labelsCurso[indiceLabel].Text = row["NOME_CURSO"].ToString();
                labelsCurso[indiceLabel].Tag = row;

                // Verificar se há disciplinas associadas
                string disciplinas = row["NOME_DISCIPLINA"] != DBNull.Value ? row["NOME_DISCIPLINA"].ToString() : "Nenhuma disciplina";
                labelsDisciplina[indiceLabel].Text = disciplinas;
                labelsDisciplina[indiceLabel].Tag = row;
            }

            AtualizarControlePaginacao();
        }

        private void AtualizarControlePaginacao()
        {
            lbl_paginacao_visualizarprofessores.Text = $"Página {paginaAtual + 1} de {totalPaginas}";
            btn_anterior_visualizarprofessores.Enabled = paginaAtual > 0;
            btn_proximo_visualizarprofessores.Enabled = paginaAtual < totalPaginas - 1;
        }

        private void btn_anterior_paginacao_Click(object sender, EventArgs e)
        {
            if (paginaAtual > 0)
            {
                paginaAtual--;
                ExibirDadosPagina();
            }
        }

        private void btn_proximo_paginacao_Click(object sender, EventArgs e)
        {
            if (paginaAtual < totalPaginas - 1)
            {
                paginaAtual++;
                ExibirDadosPagina();
            }
        }

        private void btn_voltar_visualizarprofessores_Click(object sender, EventArgs e)
        {
            // Cria uma nova instância da tela principal e a exibe
            ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide(); // Oculta a tela atual em vez de fechá-la
        }

        private void btn_minimizar_visualizarprofessores_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_visualizarprofessores_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_fechar_visualizarprofessores_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a aplicação
            }
        }

        private void IniciarEdicao(Label label, string campo)
        {
            if (!modoEdicao || label.Tag == null) return;

            // Criar TextBox para edição
            TextBox txtEdicao = new TextBox();
            txtEdicao.Text = label.Text;
            txtEdicao.Size = label.Size;
            txtEdicao.Location = label.Location;
            txtEdicao.Font = label.Font;
            txtEdicao.Tag = new { Label = label, Campo = campo, Row = label.Tag };

            // Eventos do TextBox
            txtEdicao.KeyDown += (s, e) => {
                if (e.KeyCode == Keys.Enter)
                {
                    FinalizarEdicao(txtEdicao);
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    CancelarEdicao(txtEdicao);
                }
            };

            txtEdicao.LostFocus += (s, e) => FinalizarEdicao(txtEdicao);

            // Adicionar TextBox ao painel na mesma posição do label
            TableLayoutPanelCellPosition posicao = pnl_professores.GetCellPosition(label);
            pnl_professores.Controls.Add(txtEdicao, posicao.Column, posicao.Row);

            // Ocultar label e focar no TextBox
            label.Visible = false;
            txtEdicao.Focus();
            txtEdicao.SelectAll();

            textBoxesEdicao.Add(txtEdicao);
        }

        private void FinalizarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;
            string campo = tagInfo.Campo;
            DataRow row = tagInfo.Row;

            // Atualizar o valor na DataRow (apenas nome do professor e curso são editáveis)
            if (campo == "NOME_DISCIPLINA")
            {
                // Disciplinas não são editáveis diretamente
                MessageBox.Show("As disciplinas não podem ser editadas diretamente. Elas são associadas através do curso.", "Informação", MessageBoxButtons.OK, MessageBoxIcon.Information);
                CancelarEdicao(txtEdicao);
                return;
            }
            else
            {
                row[campo] = txtEdicao.Text;
                label.Text = txtEdicao.Text;
            }

            // Remover TextBox e mostrar label
            pnl_professores.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        private void CancelarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;

            // Remover TextBox e mostrar label sem alterar dados
            pnl_professores.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        private void btn_editarprofessores_Click(object sender, EventArgs e)
        {
            modoEdicao = true;
            btn_salvar_visualizarprofessores.Visible = true;
            MessageBox.Show("Modo de edição ativado. Dê duplo clique nos campos para editá-los.", "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btn_salvarprofessores_Click(object sender, EventArgs e)
        {
            try
            {
                // Finalizar todas as edições pendentes
                foreach (var txtBox in textBoxesEdicao.ToList())
                {
                    FinalizarEdicao(txtBox);
                }

                // Salvar alterações no banco de dados
                foreach (DataRow row in dadosProfessores.Rows)
                {
                    if (row.RowState == DataRowState.Modified)
                    {
                        cmdBanco.AtualizarProfessor(
                            Convert.ToInt32(row["ID_PROFESSOR"]),
                            row["NOME_PROFESSOR"].ToString(),
                            row["NOME_CURSO"].ToString()
                        );
                    }
                }

                MessageBox.Show("Alterações salvas com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Recarregar dados e sair do modo edição
                modoEdicao = false;
                btn_salvar_visualizarprofessores.Visible = false;
                CarregarProfessores();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao salvar alterações: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_excluir_professores_Click(object sender, EventArgs e)
        {
            // Verificar se há checkboxes selecionados
            var checkboxesSelecionados = checkBoxes.Where(cb => cb.Visible && cb.Checked).ToList();

            if (checkboxesSelecionados.Count == 0)
            {
                MessageBox.Show("Selecione pelo menos um professor para excluir.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Obter nomes dos professores selecionados para confirmação
            var nomesProfessores = checkboxesSelecionados
                .Select(cb => ((DataRow)cb.Tag)["NOME_PROFESSOR"].ToString())
                .ToList();

            string mensagemConfirmacao;
            if (nomesProfessores.Count == 1)
            {
                mensagemConfirmacao = $"Deseja realmente excluir o professor '{nomesProfessores[0]}'?";
            }
            else
            {
                mensagemConfirmacao = $"Deseja realmente excluir {nomesProfessores.Count} professores selecionados?\n\n" +
                                    string.Join("\n", nomesProfessores.Take(5)) +
                                    (nomesProfessores.Count > 5 ? "\n..." : "");
            }

            // Confirmar exclusão
            DialogResult resultado = MessageBox.Show(
                mensagemConfirmacao,
                "Confirmar Exclusão",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                try
                {
                    int professoresExcluidos = 0;
                    var erros = new List<string>();

                    foreach (var checkbox in checkboxesSelecionados)
                    {
                        DataRow row = (DataRow)checkbox.Tag;
                        int idProfessor = Convert.ToInt32(row["ID_PROFESSOR"]);
                        string nomeProfessor = row["NOME_PROFESSOR"].ToString();

                        try
                        {
                            cmdBanco.DeletarProfessor(idProfessor);
                            professoresExcluidos++;
                        }
                        catch (Exception ex)
                        {
                            erros.Add($"Erro ao excluir '{nomeProfessor}': {ex.Message}");
                        }
                    }

                    // Exibir resultado
                    if (erros.Count == 0)
                    {
                        string mensagemSucesso = professoresExcluidos == 1
                            ? "Professor excluído com sucesso!"
                            : $"{professoresExcluidos} professores excluídos com sucesso!";

                        MessageBox.Show(mensagemSucesso, "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        string mensagemErro = $"{professoresExcluidos} professores excluídos com sucesso.\n\n" +
                                            $"Erros encontrados:\n{string.Join("\n", erros)}";
                        MessageBox.Show(mensagemErro, "Resultado da Exclusão", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }

                    // Recarregar dados
                    CarregarProfessores();
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Erro inesperado ao excluir professores: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void label3_Click(object sender, EventArgs e)
        {

        }
    }
}
