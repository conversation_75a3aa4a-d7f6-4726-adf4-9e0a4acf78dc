﻿namespace ProjetoIntegrador.Coordenador.C_VisualizarDisciplinas
{
    partial class C_VisualizarDisciplinas
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(C_VisualizarDisciplinas));
            pnl_header = new Panel();
            btn_voltar_visualizardisponibilidades = new Button();
            btn_minimizar_visualizardisponibilidades = new Button();
            btn_maximizar_visualizardisponibilidades = new Button();
            lbl_titulo = new Label();
            btn_fechar_visualizardisponibilidades = new Button();
            lbl_disciplinas = new Label();
            pnl_disciplinas = new TableLayoutPanel();
            lbl_cargahoraria = new Label();
            lbl_aulas = new Label();
            lbl_curso = new Label();
            lbl_nome = new Label();
            cb_linha1_visualizardisciplinas = new CheckBox();
            cb_linha2_visualizardisciplinas = new CheckBox();
            cb_linha3_visualizardisciplinas = new CheckBox();
            cb_linha4_visualizardisciplinas = new CheckBox();
            cb_linha5_visualizardisciplinas = new CheckBox();
            cb_linha6_visualizardisciplinas = new CheckBox();
            cb_linha7_visualizardisciplinas = new CheckBox();
            cb_linha8_visualizardisciplinas = new CheckBox();
            cb_linha9_visualizardisciplinas = new CheckBox();
            btn_editardisciplinas_visualizardisponibilidades = new Button();
            btn_salvardisciplinas_visualizardisponibilidades = new Button();
            btn_excluir_disciplinas = new Button();
            btn_anterior_paginacao = new Button();
            btn_proximo_paginacao = new Button();
            lbl_paginacao = new Label();
            pnl_header.SuspendLayout();
            pnl_disciplinas.SuspendLayout();
            SuspendLayout();
            // 
            // pnl_header
            // 
            pnl_header.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pnl_header.BackColor = Color.MidnightBlue;
            pnl_header.Controls.Add(btn_voltar_visualizardisponibilidades);
            pnl_header.Controls.Add(btn_minimizar_visualizardisponibilidades);
            pnl_header.Controls.Add(btn_maximizar_visualizardisponibilidades);
            pnl_header.Controls.Add(lbl_titulo);
            pnl_header.Controls.Add(btn_fechar_visualizardisponibilidades);
            pnl_header.Location = new Point(0, 0);
            pnl_header.Name = "pnl_header";
            pnl_header.Size = new Size(1280, 63);
            pnl_header.TabIndex = 2;
            // 
            // btn_voltar_visualizardisponibilidades
            // 
            btn_voltar_visualizardisponibilidades.BackColor = Color.MidnightBlue;
            btn_voltar_visualizardisponibilidades.BackgroundImage = (Image)resources.GetObject("btn_voltar_visualizardisponibilidades.BackgroundImage");
            btn_voltar_visualizardisponibilidades.BackgroundImageLayout = ImageLayout.Zoom;
            btn_voltar_visualizardisponibilidades.FlatAppearance.BorderSize = 0;
            btn_voltar_visualizardisponibilidades.FlatStyle = FlatStyle.Flat;
            btn_voltar_visualizardisponibilidades.Location = new Point(35, 18);
            btn_voltar_visualizardisponibilidades.Name = "btn_voltar_visualizardisponibilidades";
            btn_voltar_visualizardisponibilidades.Size = new Size(30, 21);
            btn_voltar_visualizardisponibilidades.TabIndex = 23;
            btn_voltar_visualizardisponibilidades.UseVisualStyleBackColor = false;
            btn_voltar_visualizardisponibilidades.Click += btn_voltar_visualizardisponibilidades_Click;
            // 
            // btn_minimizar_visualizardisponibilidades
            // 
            btn_minimizar_visualizardisponibilidades.BackgroundImage = (Image)resources.GetObject("btn_minimizar_visualizardisponibilidades.BackgroundImage");
            btn_minimizar_visualizardisponibilidades.BackgroundImageLayout = ImageLayout.Stretch;
            btn_minimizar_visualizardisponibilidades.FlatAppearance.BorderSize = 0;
            btn_minimizar_visualizardisponibilidades.FlatStyle = FlatStyle.Flat;
            btn_minimizar_visualizardisponibilidades.Location = new Point(1173, 22);
            btn_minimizar_visualizardisponibilidades.Name = "btn_minimizar_visualizardisponibilidades";
            btn_minimizar_visualizardisponibilidades.Size = new Size(21, 19);
            btn_minimizar_visualizardisponibilidades.TabIndex = 22;
            btn_minimizar_visualizardisponibilidades.UseVisualStyleBackColor = true;
            btn_minimizar_visualizardisponibilidades.Click += btn_minimizar_visualizardisponibilidades_Click;
            // 
            // btn_maximizar_visualizardisponibilidades
            // 
            btn_maximizar_visualizardisponibilidades.BackgroundImage = (Image)resources.GetObject("btn_maximizar_visualizardisponibilidades.BackgroundImage");
            btn_maximizar_visualizardisponibilidades.BackgroundImageLayout = ImageLayout.Zoom;
            btn_maximizar_visualizardisponibilidades.FlatAppearance.BorderSize = 0;
            btn_maximizar_visualizardisponibilidades.FlatStyle = FlatStyle.Flat;
            btn_maximizar_visualizardisponibilidades.Location = new Point(1209, 18);
            btn_maximizar_visualizardisponibilidades.Name = "btn_maximizar_visualizardisponibilidades";
            btn_maximizar_visualizardisponibilidades.Size = new Size(19, 23);
            btn_maximizar_visualizardisponibilidades.TabIndex = 21;
            btn_maximizar_visualizardisponibilidades.UseVisualStyleBackColor = true;
            btn_maximizar_visualizardisponibilidades.Click += btn_maximizar_visualizardisponibilidades_Click;
            // 
            // lbl_titulo
            // 
            lbl_titulo.AutoSize = true;
            lbl_titulo.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_titulo.ForeColor = SystemColors.ButtonHighlight;
            lbl_titulo.Location = new Point(632, 18);
            lbl_titulo.Name = "lbl_titulo";
            lbl_titulo.Size = new Size(62, 25);
            lbl_titulo.TabIndex = 1;
            lbl_titulo.Text = "SEHD";
            // 
            // btn_fechar_visualizardisponibilidades
            // 
            btn_fechar_visualizardisponibilidades.BackColor = Color.MidnightBlue;
            btn_fechar_visualizardisponibilidades.BackgroundImage = (Image)resources.GetObject("btn_fechar_visualizardisponibilidades.BackgroundImage");
            btn_fechar_visualizardisponibilidades.BackgroundImageLayout = ImageLayout.Zoom;
            btn_fechar_visualizardisponibilidades.FlatAppearance.BorderSize = 0;
            btn_fechar_visualizardisponibilidades.FlatStyle = FlatStyle.Flat;
            btn_fechar_visualizardisponibilidades.Location = new Point(1234, 18);
            btn_fechar_visualizardisponibilidades.Name = "btn_fechar_visualizardisponibilidades";
            btn_fechar_visualizardisponibilidades.Size = new Size(34, 21);
            btn_fechar_visualizardisponibilidades.TabIndex = 20;
            btn_fechar_visualizardisponibilidades.UseVisualStyleBackColor = false;
            btn_fechar_visualizardisponibilidades.Click += btn_fechar_visualizardisponibilidades_Click;
            // 
            // lbl_disciplinas
            // 
            lbl_disciplinas.AutoSize = true;
            lbl_disciplinas.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_disciplinas.ForeColor = Color.MidnightBlue;
            lbl_disciplinas.Location = new Point(616, 101);
            lbl_disciplinas.Name = "lbl_disciplinas";
            lbl_disciplinas.Size = new Size(105, 25);
            lbl_disciplinas.TabIndex = 22;
            lbl_disciplinas.Text = "Disciplinas";
            // 
            // pnl_disciplinas
            // 
            pnl_disciplinas.BackColor = Color.DarkGray;
            pnl_disciplinas.CellBorderStyle = TableLayoutPanelCellBorderStyle.Inset;
            pnl_disciplinas.ColumnCount = 4;
            pnl_disciplinas.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33556F));
            pnl_disciplinas.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 22.2214813F));
            pnl_disciplinas.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 22.2214813F));
            pnl_disciplinas.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 22.2214813F));
            pnl_disciplinas.Controls.Add(lbl_cargahoraria, 3, 0);
            pnl_disciplinas.Controls.Add(lbl_aulas, 2, 0);
            pnl_disciplinas.Controls.Add(lbl_curso, 1, 0);
            pnl_disciplinas.Controls.Add(lbl_nome, 0, 0);
            pnl_disciplinas.Location = new Point(248, 147);
            pnl_disciplinas.Name = "pnl_disciplinas";
            pnl_disciplinas.RowCount = 10;
            pnl_disciplinas.RowStyles.Add(new RowStyle(SizeType.Absolute, 25F));
            pnl_disciplinas.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disciplinas.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disciplinas.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disciplinas.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disciplinas.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disciplinas.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disciplinas.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disciplinas.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disciplinas.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disciplinas.Size = new Size(871, 483);
            pnl_disciplinas.TabIndex = 21;
            pnl_disciplinas.Paint += pnl_disciplinas_Paint;
            // 
            // lbl_cargahoraria
            // 
            lbl_cargahoraria.AutoSize = true;
            lbl_cargahoraria.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_cargahoraria.ForeColor = Color.MidnightBlue;
            lbl_cargahoraria.Location = new Point(680, 2);
            lbl_cargahoraria.Name = "lbl_cargahoraria";
            lbl_cargahoraria.Padding = new Padding(35, 2, 0, 0);
            lbl_cargahoraria.Size = new Size(150, 23);
            lbl_cargahoraria.TabIndex = 24;
            lbl_cargahoraria.Text = "Carga Horária";
            // 
            // lbl_aulas
            // 
            lbl_aulas.AutoSize = true;
            lbl_aulas.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_aulas.ForeColor = Color.MidnightBlue;
            lbl_aulas.Location = new Point(487, 2);
            lbl_aulas.Name = "lbl_aulas";
            lbl_aulas.Padding = new Padding(65, 2, 0, 0);
            lbl_aulas.Size = new Size(117, 23);
            lbl_aulas.TabIndex = 21;
            lbl_aulas.Text = "Aulas";
            // 
            // lbl_curso
            // 
            lbl_curso.AutoSize = true;
            lbl_curso.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_curso.ForeColor = Color.MidnightBlue;
            lbl_curso.Location = new Point(294, 2);
            lbl_curso.Name = "lbl_curso";
            lbl_curso.Padding = new Padding(67, 2, 0, 0);
            lbl_curso.Size = new Size(120, 23);
            lbl_curso.TabIndex = 20;
            lbl_curso.Text = "Curso";
            // 
            // lbl_nome
            // 
            lbl_nome.AutoSize = true;
            lbl_nome.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_nome.ForeColor = Color.MidnightBlue;
            lbl_nome.Location = new Point(5, 2);
            lbl_nome.Name = "lbl_nome";
            lbl_nome.Padding = new Padding(110, 2, 0, 0);
            lbl_nome.Size = new Size(167, 23);
            lbl_nome.TabIndex = 0;
            lbl_nome.Text = "Nome";
            // 
            // cb_linha1_visualizardisciplinas
            // 
            cb_linha1_visualizardisciplinas.AutoSize = true;
            cb_linha1_visualizardisciplinas.Location = new Point(220, 196);
            cb_linha1_visualizardisciplinas.Name = "cb_linha1_visualizardisciplinas";
            cb_linha1_visualizardisciplinas.Size = new Size(15, 14);
            cb_linha1_visualizardisciplinas.TabIndex = 25;
            cb_linha1_visualizardisciplinas.UseVisualStyleBackColor = true;
            cb_linha1_visualizardisciplinas.Visible = false;
            // 
            // cb_linha2_visualizardisciplinas
            // 
            cb_linha2_visualizardisciplinas.AutoSize = true;
            cb_linha2_visualizardisciplinas.Location = new Point(220, 247);
            cb_linha2_visualizardisciplinas.Name = "cb_linha2_visualizardisciplinas";
            cb_linha2_visualizardisciplinas.Size = new Size(15, 14);
            cb_linha2_visualizardisciplinas.TabIndex = 26;
            cb_linha2_visualizardisciplinas.UseVisualStyleBackColor = true;
            cb_linha2_visualizardisciplinas.Visible = false;
            // 
            // cb_linha3_visualizardisciplinas
            // 
            cb_linha3_visualizardisciplinas.AutoSize = true;
            cb_linha3_visualizardisciplinas.Location = new Point(220, 293);
            cb_linha3_visualizardisciplinas.Name = "cb_linha3_visualizardisciplinas";
            cb_linha3_visualizardisciplinas.Size = new Size(15, 14);
            cb_linha3_visualizardisciplinas.TabIndex = 27;
            cb_linha3_visualizardisciplinas.UseVisualStyleBackColor = true;
            cb_linha3_visualizardisciplinas.Visible = false;
            // 
            // cb_linha4_visualizardisciplinas
            // 
            cb_linha4_visualizardisciplinas.AutoSize = true;
            cb_linha4_visualizardisciplinas.Location = new Point(220, 345);
            cb_linha4_visualizardisciplinas.Name = "cb_linha4_visualizardisciplinas";
            cb_linha4_visualizardisciplinas.Size = new Size(15, 14);
            cb_linha4_visualizardisciplinas.TabIndex = 28;
            cb_linha4_visualizardisciplinas.UseVisualStyleBackColor = true;
            cb_linha4_visualizardisciplinas.Visible = false;
            // 
            // cb_linha5_visualizardisciplinas
            // 
            cb_linha5_visualizardisciplinas.AutoSize = true;
            cb_linha5_visualizardisciplinas.Location = new Point(220, 395);
            cb_linha5_visualizardisciplinas.Name = "cb_linha5_visualizardisciplinas";
            cb_linha5_visualizardisciplinas.Size = new Size(15, 14);
            cb_linha5_visualizardisciplinas.TabIndex = 29;
            cb_linha5_visualizardisciplinas.UseVisualStyleBackColor = true;
            cb_linha5_visualizardisciplinas.Visible = false;
            // 
            // cb_linha6_visualizardisciplinas
            // 
            cb_linha6_visualizardisciplinas.AutoSize = true;
            cb_linha6_visualizardisciplinas.Location = new Point(220, 443);
            cb_linha6_visualizardisciplinas.Name = "cb_linha6_visualizardisciplinas";
            cb_linha6_visualizardisciplinas.Size = new Size(15, 14);
            cb_linha6_visualizardisciplinas.TabIndex = 30;
            cb_linha6_visualizardisciplinas.UseVisualStyleBackColor = true;
            cb_linha6_visualizardisciplinas.Visible = false;
            // 
            // cb_linha7_visualizardisciplinas
            // 
            cb_linha7_visualizardisciplinas.AutoSize = true;
            cb_linha7_visualizardisciplinas.Location = new Point(220, 496);
            cb_linha7_visualizardisciplinas.Name = "cb_linha7_visualizardisciplinas";
            cb_linha7_visualizardisciplinas.Size = new Size(15, 14);
            cb_linha7_visualizardisciplinas.TabIndex = 31;
            cb_linha7_visualizardisciplinas.UseVisualStyleBackColor = true;
            cb_linha7_visualizardisciplinas.Visible = false;
            // 
            // cb_linha8_visualizardisciplinas
            // 
            cb_linha8_visualizardisciplinas.AutoSize = true;
            cb_linha8_visualizardisciplinas.Location = new Point(220, 545);
            cb_linha8_visualizardisciplinas.Name = "cb_linha8_visualizardisciplinas";
            cb_linha8_visualizardisciplinas.Size = new Size(15, 14);
            cb_linha8_visualizardisciplinas.TabIndex = 32;
            cb_linha8_visualizardisciplinas.UseVisualStyleBackColor = true;
            cb_linha8_visualizardisciplinas.Visible = false;
            // 
            // cb_linha9_visualizardisciplinas
            // 
            cb_linha9_visualizardisciplinas.AutoSize = true;
            cb_linha9_visualizardisciplinas.Location = new Point(220, 596);
            cb_linha9_visualizardisciplinas.Name = "cb_linha9_visualizardisciplinas";
            cb_linha9_visualizardisciplinas.Size = new Size(15, 14);
            cb_linha9_visualizardisciplinas.TabIndex = 33;
            cb_linha9_visualizardisciplinas.UseVisualStyleBackColor = true;
            cb_linha9_visualizardisciplinas.Visible = false;
            // 
            // btn_editardisciplinas_visualizardisponibilidades
            // 
            btn_editardisciplinas_visualizardisponibilidades.BackColor = Color.Transparent;
            btn_editardisciplinas_visualizardisponibilidades.BackgroundImage = (Image)resources.GetObject("btn_editardisciplinas_visualizardisponibilidades.BackgroundImage");
            btn_editardisciplinas_visualizardisponibilidades.BackgroundImageLayout = ImageLayout.Zoom;
            btn_editardisciplinas_visualizardisponibilidades.FlatAppearance.BorderSize = 0;
            btn_editardisciplinas_visualizardisponibilidades.FlatStyle = FlatStyle.Flat;
            btn_editardisciplinas_visualizardisponibilidades.ForeColor = SystemColors.ControlLightLight;
            btn_editardisciplinas_visualizardisponibilidades.Location = new Point(994, 650);
            btn_editardisciplinas_visualizardisponibilidades.Name = "btn_editardisciplinas_visualizardisponibilidades";
            btn_editardisciplinas_visualizardisponibilidades.Size = new Size(125, 33);
            btn_editardisciplinas_visualizardisponibilidades.TabIndex = 24;
            btn_editardisciplinas_visualizardisponibilidades.Text = "Editar";
            btn_editardisciplinas_visualizardisponibilidades.UseVisualStyleBackColor = false;
            btn_editardisciplinas_visualizardisponibilidades.Click += btn_editardisciplinas_visualizardisponibilidades_Click;
            // 
            // btn_salvardisciplinas_visualizardisponibilidades
            // 
            btn_salvardisciplinas_visualizardisponibilidades.BackColor = Color.Transparent;
            btn_salvardisciplinas_visualizardisponibilidades.BackgroundImage = (Image)resources.GetObject("btn_salvardisciplinas_visualizardisponibilidades.BackgroundImage");
            btn_salvardisciplinas_visualizardisponibilidades.BackgroundImageLayout = ImageLayout.Zoom;
            btn_salvardisciplinas_visualizardisponibilidades.FlatAppearance.BorderSize = 0;
            btn_salvardisciplinas_visualizardisponibilidades.FlatStyle = FlatStyle.Flat;
            btn_salvardisciplinas_visualizardisponibilidades.ForeColor = SystemColors.ControlLightLight;
            btn_salvardisciplinas_visualizardisponibilidades.Location = new Point(718, 650);
            btn_salvardisciplinas_visualizardisponibilidades.Name = "btn_salvardisciplinas_visualizardisponibilidades";
            btn_salvardisciplinas_visualizardisponibilidades.Size = new Size(125, 33);
            btn_salvardisciplinas_visualizardisponibilidades.TabIndex = 33;
            btn_salvardisciplinas_visualizardisponibilidades.Text = "Salvar";
            btn_salvardisciplinas_visualizardisponibilidades.UseVisualStyleBackColor = false;
            btn_salvardisciplinas_visualizardisponibilidades.Visible = false;
            btn_salvardisciplinas_visualizardisponibilidades.Click += btn_salvardisciplinas_visualizardisponibilidades_Click;
            // 
            // btn_excluir_disciplinas
            // 
            btn_excluir_disciplinas.BackColor = Color.Transparent;
            btn_excluir_disciplinas.BackgroundImage = (Image)resources.GetObject("btn_excluir_disciplinas.BackgroundImage");
            btn_excluir_disciplinas.BackgroundImageLayout = ImageLayout.Zoom;
            btn_excluir_disciplinas.FlatAppearance.BorderSize = 0;
            btn_excluir_disciplinas.FlatStyle = FlatStyle.Flat;
            btn_excluir_disciplinas.ForeColor = SystemColors.ControlLightLight;
            btn_excluir_disciplinas.Location = new Point(855, 650);
            btn_excluir_disciplinas.Name = "btn_excluir_disciplinas";
            btn_excluir_disciplinas.Size = new Size(125, 33);
            btn_excluir_disciplinas.TabIndex = 37;
            btn_excluir_disciplinas.Text = "Excluir";
            btn_excluir_disciplinas.UseVisualStyleBackColor = false;
            btn_excluir_disciplinas.Click += btn_excluir_disciplinas_Click;
            // 
            // btn_anterior_paginacao
            // 
            btn_anterior_paginacao.BackColor = Color.Transparent;
            btn_anterior_paginacao.BackgroundImage = (Image)resources.GetObject("btn_anterior_paginacao.BackgroundImage");
            btn_anterior_paginacao.BackgroundImageLayout = ImageLayout.Zoom;
            btn_anterior_paginacao.FlatAppearance.BorderSize = 0;
            btn_anterior_paginacao.FlatStyle = FlatStyle.Flat;
            btn_anterior_paginacao.ForeColor = Color.White;
            btn_anterior_paginacao.Location = new Point(248, 653);
            btn_anterior_paginacao.Name = "btn_anterior_paginacao";
            btn_anterior_paginacao.Size = new Size(37, 26);
            btn_anterior_paginacao.TabIndex = 34;
            btn_anterior_paginacao.UseVisualStyleBackColor = false;
            btn_anterior_paginacao.Click += btn_anterior_paginacao_Click;
            // 
            // btn_proximo_paginacao
            // 
            btn_proximo_paginacao.BackColor = Color.Transparent;
            btn_proximo_paginacao.BackgroundImage = (Image)resources.GetObject("btn_proximo_paginacao.BackgroundImage");
            btn_proximo_paginacao.BackgroundImageLayout = ImageLayout.Zoom;
            btn_proximo_paginacao.FlatAppearance.BorderSize = 0;
            btn_proximo_paginacao.FlatStyle = FlatStyle.Flat;
            btn_proximo_paginacao.ForeColor = Color.White;
            btn_proximo_paginacao.Location = new Point(397, 653);
            btn_proximo_paginacao.Name = "btn_proximo_paginacao";
            btn_proximo_paginacao.Size = new Size(37, 26);
            btn_proximo_paginacao.TabIndex = 35;
            btn_proximo_paginacao.UseVisualStyleBackColor = false;
            btn_proximo_paginacao.Click += btn_proximo_paginacao_Click;
            // 
            // lbl_paginacao
            // 
            lbl_paginacao.AutoSize = true;
            lbl_paginacao.Font = new Font("Segoe UI", 10F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_paginacao.ForeColor = Color.MidnightBlue;
            lbl_paginacao.Location = new Point(291, 656);
            lbl_paginacao.Name = "lbl_paginacao";
            lbl_paginacao.Size = new Size(100, 19);
            lbl_paginacao.TabIndex = 36;
            lbl_paginacao.Text = "Página 1 de 1";
            lbl_paginacao.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // C_VisualizarDisciplinas
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1280, 702);
            Controls.Add(lbl_paginacao);
            Controls.Add(btn_proximo_paginacao);
            Controls.Add(btn_anterior_paginacao);
            Controls.Add(btn_excluir_disciplinas);
            Controls.Add(btn_salvardisciplinas_visualizardisponibilidades);
            Controls.Add(btn_editardisciplinas_visualizardisponibilidades);
            Controls.Add(cb_linha1_visualizardisciplinas);
            Controls.Add(cb_linha2_visualizardisciplinas);
            Controls.Add(cb_linha3_visualizardisciplinas);
            Controls.Add(cb_linha4_visualizardisciplinas);
            Controls.Add(cb_linha5_visualizardisciplinas);
            Controls.Add(cb_linha6_visualizardisciplinas);
            Controls.Add(cb_linha7_visualizardisciplinas);
            Controls.Add(cb_linha8_visualizardisciplinas);
            Controls.Add(cb_linha9_visualizardisciplinas);
            Controls.Add(lbl_disciplinas);
            Controls.Add(pnl_disciplinas);
            Controls.Add(pnl_header);
            FormBorderStyle = FormBorderStyle.None;
            Name = "C_VisualizarDisciplinas";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "C_VisualizarDisciplinas";
            pnl_header.ResumeLayout(false);
            pnl_header.PerformLayout();
            pnl_disciplinas.ResumeLayout(false);
            pnl_disciplinas.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Panel pnl_header;
        private Button btn_voltar_visualizardisponibilidades;
        private Button btn_minimizar_visualizardisponibilidades;
        private Button btn_maximizar_visualizardisponibilidades;
        private Label lbl_titulo;
        private Button btn_fechar_visualizardisponibilidades;
        private Label lbl_disciplinas;
        private TableLayoutPanel pnl_disciplinas;
        private Label lbl_aulas;
        private Label lbl_curso;
        private Label lbl_nome;
        private Label lbl_cargahoraria;
        private CheckBox cb_linha1_visualizardisciplinas;
        private CheckBox cb_linha2_visualizardisciplinas;
        private CheckBox cb_linha3_visualizardisciplinas;
        private CheckBox cb_linha4_visualizardisciplinas;
        private CheckBox cb_linha5_visualizardisciplinas;
        private CheckBox cb_linha6_visualizardisciplinas;
        private CheckBox cb_linha7_visualizardisciplinas;
        private CheckBox cb_linha8_visualizardisciplinas;
        private CheckBox cb_linha9_visualizardisciplinas;
        private Button btn_editardisciplinas_visualizardisponibilidades;
        private Button btn_salvardisciplinas_visualizardisponibilidades;
        private Button btn_excluir_disciplinas;
        private Button btn_anterior_paginacao;
        private Button btn_proximo_paginacao;
        private Label lbl_paginacao;
    }
}