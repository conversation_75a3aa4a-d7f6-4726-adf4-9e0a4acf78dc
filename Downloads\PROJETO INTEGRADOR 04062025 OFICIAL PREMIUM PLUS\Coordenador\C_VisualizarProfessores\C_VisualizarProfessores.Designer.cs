﻿namespace ProjetoIntegrador.Coordenador.C_VisualizarProfessores
{
    partial class C_VisualizarProfessores
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(C_VisualizarProfessores));
            pnl_professores = new TableLayoutPanel();
            lbl_disciplina = new Label();
            lbl_curso = new Label();
            lbl_nome = new Label();
            chb_linha1_visualizarprofessores = new CheckBox();
            chb_linha2_visualizarprofessores = new CheckBox();
            chb_linha3_visualizarprofessores = new CheckBox();
            chb_linha4_visualizarprofessores = new CheckBox();
            chb_linha5_visualizarprofessores = new CheckBox();
            chb_linha6_visualizarprofessores = new CheckBox();
            chb_linha7_visualizarprofessores = new CheckBox();
            chb_linha8_visualizarprofessores = new CheckBox();
            chb_linha9_visualizarprofessores = new CheckBox();
            pnl_header = new Panel();
            btn_voltar_visualizarprofessores = new Button();
            btn_minimizar_visualizarprofessores = new Button();
            btn_maximizar_visualizarprofessores = new Button();
            lbl_titulo = new Label();
            btn_fechar_visualizarprofessores = new Button();
            lbl_professores = new Label();
            btn_editar_visualizarprofessores = new Button();
            btn_salvar_visualizarprofessores = new Button();
            lbl_paginacao_visualizarprofessores = new Label();
            btn_proximo_visualizarprofessores = new Button();
            btn_anterior_visualizarprofessores = new Button();
            btn_excluir_disciplinas = new Button();
            pnl_professores.SuspendLayout();
            pnl_header.SuspendLayout();
            SuspendLayout();
            // 
            // pnl_professores
            // 
            pnl_professores.BackColor = Color.DarkGray;
            pnl_professores.CellBorderStyle = TableLayoutPanelCellBorderStyle.Inset;
            pnl_professores.ColumnCount = 3;
            pnl_professores.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33704F));
            pnl_professores.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.3314819F));
            pnl_professores.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.3314819F));
            pnl_professores.Controls.Add(lbl_disciplina, 2, 0);
            pnl_professores.Controls.Add(lbl_curso, 1, 0);
            pnl_professores.Controls.Add(lbl_nome, 0, 0);
            pnl_professores.Location = new Point(248, 147);
            pnl_professores.Name = "pnl_professores";
            pnl_professores.RowCount = 10;
            pnl_professores.RowStyles.Add(new RowStyle(SizeType.Absolute, 25F));
            pnl_professores.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_professores.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_professores.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_professores.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_professores.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_professores.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_professores.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_professores.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_professores.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_professores.Size = new Size(871, 483);
            pnl_professores.TabIndex = 25;
            // 
            // lbl_disciplina
            // 
            lbl_disciplina.AutoSize = true;
            lbl_disciplina.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_disciplina.ForeColor = Color.MidnightBlue;
            lbl_disciplina.Location = new Point(583, 2);
            lbl_disciplina.Name = "lbl_disciplina";
            lbl_disciplina.Padding = new Padding(100, 2, 0, 0);
            lbl_disciplina.Size = new Size(186, 23);
            lbl_disciplina.TabIndex = 21;
            lbl_disciplina.Text = "Disciplina";
            lbl_disciplina.Click += label3_Click;
            // 
            // lbl_curso
            // 
            lbl_curso.AutoSize = true;
            lbl_curso.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_curso.ForeColor = Color.MidnightBlue;
            lbl_curso.Location = new Point(294, 2);
            lbl_curso.Name = "lbl_curso";
            lbl_curso.Padding = new Padding(110, 2, 0, 0);
            lbl_curso.Size = new Size(163, 23);
            lbl_curso.TabIndex = 20;
            lbl_curso.Text = "Curso";
            // 
            // lbl_nome
            // 
            lbl_nome.AutoSize = true;
            lbl_nome.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_nome.ForeColor = Color.MidnightBlue;
            lbl_nome.Location = new Point(5, 2);
            lbl_nome.Name = "lbl_nome";
            lbl_nome.Padding = new Padding(110, 2, 0, 0);
            lbl_nome.Size = new Size(167, 23);
            lbl_nome.TabIndex = 0;
            lbl_nome.Text = "Nome";
            // 
            // chb_linha1_visualizarprofessores
            // 
            chb_linha1_visualizarprofessores.AutoSize = true;
            chb_linha1_visualizarprofessores.Location = new Point(220, 196);
            chb_linha1_visualizarprofessores.Name = "chb_linha1_visualizarprofessores";
            chb_linha1_visualizarprofessores.Size = new Size(15, 14);
            chb_linha1_visualizarprofessores.TabIndex = 22;
            chb_linha1_visualizarprofessores.UseVisualStyleBackColor = true;
            // 
            // chb_linha2_visualizarprofessores
            // 
            chb_linha2_visualizarprofessores.AutoSize = true;
            chb_linha2_visualizarprofessores.Location = new Point(220, 247);
            chb_linha2_visualizarprofessores.Name = "chb_linha2_visualizarprofessores";
            chb_linha2_visualizarprofessores.Size = new Size(15, 14);
            chb_linha2_visualizarprofessores.TabIndex = 23;
            chb_linha2_visualizarprofessores.UseVisualStyleBackColor = true;
            // 
            // chb_linha3_visualizarprofessores
            // 
            chb_linha3_visualizarprofessores.AutoSize = true;
            chb_linha3_visualizarprofessores.Location = new Point(220, 293);
            chb_linha3_visualizarprofessores.Name = "chb_linha3_visualizarprofessores";
            chb_linha3_visualizarprofessores.Size = new Size(15, 14);
            chb_linha3_visualizarprofessores.TabIndex = 24;
            chb_linha3_visualizarprofessores.UseVisualStyleBackColor = true;
            // 
            // chb_linha4_visualizarprofessores
            // 
            chb_linha4_visualizarprofessores.AutoSize = true;
            chb_linha4_visualizarprofessores.Location = new Point(220, 345);
            chb_linha4_visualizarprofessores.Name = "chb_linha4_visualizarprofessores";
            chb_linha4_visualizarprofessores.Size = new Size(15, 14);
            chb_linha4_visualizarprofessores.TabIndex = 25;
            chb_linha4_visualizarprofessores.UseVisualStyleBackColor = true;
            // 
            // chb_linha5_visualizarprofessores
            // 
            chb_linha5_visualizarprofessores.AutoSize = true;
            chb_linha5_visualizarprofessores.Location = new Point(220, 395);
            chb_linha5_visualizarprofessores.Name = "chb_linha5_visualizarprofessores";
            chb_linha5_visualizarprofessores.Size = new Size(15, 14);
            chb_linha5_visualizarprofessores.TabIndex = 26;
            chb_linha5_visualizarprofessores.UseVisualStyleBackColor = true;
            // 
            // chb_linha6_visualizarprofessores
            // 
            chb_linha6_visualizarprofessores.AutoSize = true;
            chb_linha6_visualizarprofessores.Location = new Point(220, 443);
            chb_linha6_visualizarprofessores.Name = "chb_linha6_visualizarprofessores";
            chb_linha6_visualizarprofessores.Size = new Size(15, 14);
            chb_linha6_visualizarprofessores.TabIndex = 27;
            chb_linha6_visualizarprofessores.UseVisualStyleBackColor = true;
            // 
            // chb_linha7_visualizarprofessores
            // 
            chb_linha7_visualizarprofessores.AutoSize = true;
            chb_linha7_visualizarprofessores.Location = new Point(220, 496);
            chb_linha7_visualizarprofessores.Name = "chb_linha7_visualizarprofessores";
            chb_linha7_visualizarprofessores.Size = new Size(15, 14);
            chb_linha7_visualizarprofessores.TabIndex = 28;
            chb_linha7_visualizarprofessores.UseVisualStyleBackColor = true;
            // 
            // chb_linha8_visualizarprofessores
            // 
            chb_linha8_visualizarprofessores.AutoSize = true;
            chb_linha8_visualizarprofessores.Location = new Point(220, 545);
            chb_linha8_visualizarprofessores.Name = "chb_linha8_visualizarprofessores";
            chb_linha8_visualizarprofessores.Size = new Size(15, 14);
            chb_linha8_visualizarprofessores.TabIndex = 29;
            chb_linha8_visualizarprofessores.UseVisualStyleBackColor = true;
            // 
            // chb_linha9_visualizarprofessores
            // 
            chb_linha9_visualizarprofessores.AutoSize = true;
            chb_linha9_visualizarprofessores.Location = new Point(220, 596);
            chb_linha9_visualizarprofessores.Name = "chb_linha9_visualizarprofessores";
            chb_linha9_visualizarprofessores.Size = new Size(15, 14);
            chb_linha9_visualizarprofessores.TabIndex = 30;
            chb_linha9_visualizarprofessores.UseVisualStyleBackColor = true;
            // 
            // pnl_header
            // 
            pnl_header.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pnl_header.BackColor = Color.MidnightBlue;
            pnl_header.Controls.Add(btn_voltar_visualizarprofessores);
            pnl_header.Controls.Add(btn_minimizar_visualizarprofessores);
            pnl_header.Controls.Add(btn_maximizar_visualizarprofessores);
            pnl_header.Controls.Add(lbl_titulo);
            pnl_header.Controls.Add(btn_fechar_visualizarprofessores);
            pnl_header.Location = new Point(0, 0);
            pnl_header.Name = "pnl_header";
            pnl_header.Size = new Size(1280, 63);
            pnl_header.TabIndex = 29;
            // 
            // btn_voltar_visualizarprofessores
            // 
            btn_voltar_visualizarprofessores.BackColor = Color.MidnightBlue;
            btn_voltar_visualizarprofessores.BackgroundImage = (Image)resources.GetObject("btn_voltar_visualizarprofessores.BackgroundImage");
            btn_voltar_visualizarprofessores.BackgroundImageLayout = ImageLayout.Zoom;
            btn_voltar_visualizarprofessores.FlatAppearance.BorderSize = 0;
            btn_voltar_visualizarprofessores.FlatStyle = FlatStyle.Flat;
            btn_voltar_visualizarprofessores.Location = new Point(35, 18);
            btn_voltar_visualizarprofessores.Name = "btn_voltar_visualizarprofessores";
            btn_voltar_visualizarprofessores.Size = new Size(30, 21);
            btn_voltar_visualizarprofessores.TabIndex = 23;
            btn_voltar_visualizarprofessores.UseVisualStyleBackColor = false;
            btn_voltar_visualizarprofessores.Click += btn_voltar_visualizarprofessores_Click;
            // 
            // btn_minimizar_visualizarprofessores
            // 
            btn_minimizar_visualizarprofessores.BackgroundImage = (Image)resources.GetObject("btn_minimizar_visualizarprofessores.BackgroundImage");
            btn_minimizar_visualizarprofessores.BackgroundImageLayout = ImageLayout.Stretch;
            btn_minimizar_visualizarprofessores.FlatAppearance.BorderSize = 0;
            btn_minimizar_visualizarprofessores.FlatStyle = FlatStyle.Flat;
            btn_minimizar_visualizarprofessores.Location = new Point(1173, 22);
            btn_minimizar_visualizarprofessores.Name = "btn_minimizar_visualizarprofessores";
            btn_minimizar_visualizarprofessores.Size = new Size(21, 19);
            btn_minimizar_visualizarprofessores.TabIndex = 22;
            btn_minimizar_visualizarprofessores.UseVisualStyleBackColor = true;
            btn_minimizar_visualizarprofessores.Click += btn_minimizar_visualizarprofessores_Click;
            // 
            // btn_maximizar_visualizarprofessores
            // 
            btn_maximizar_visualizarprofessores.BackgroundImage = (Image)resources.GetObject("btn_maximizar_visualizarprofessores.BackgroundImage");
            btn_maximizar_visualizarprofessores.BackgroundImageLayout = ImageLayout.Zoom;
            btn_maximizar_visualizarprofessores.FlatAppearance.BorderSize = 0;
            btn_maximizar_visualizarprofessores.FlatStyle = FlatStyle.Flat;
            btn_maximizar_visualizarprofessores.Location = new Point(1209, 18);
            btn_maximizar_visualizarprofessores.Name = "btn_maximizar_visualizarprofessores";
            btn_maximizar_visualizarprofessores.Size = new Size(19, 23);
            btn_maximizar_visualizarprofessores.TabIndex = 21;
            btn_maximizar_visualizarprofessores.UseVisualStyleBackColor = true;
            btn_maximizar_visualizarprofessores.Click += btn_maximizar_visualizarprofessores_Click;
            // 
            // lbl_titulo
            // 
            lbl_titulo.AutoSize = true;
            lbl_titulo.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_titulo.ForeColor = SystemColors.ButtonHighlight;
            lbl_titulo.Location = new Point(632, 18);
            lbl_titulo.Name = "lbl_titulo";
            lbl_titulo.Size = new Size(62, 25);
            lbl_titulo.TabIndex = 1;
            lbl_titulo.Text = "SEHD";
            // 
            // btn_fechar_visualizarprofessores
            // 
            btn_fechar_visualizarprofessores.BackColor = Color.MidnightBlue;
            btn_fechar_visualizarprofessores.BackgroundImage = (Image)resources.GetObject("btn_fechar_visualizarprofessores.BackgroundImage");
            btn_fechar_visualizarprofessores.BackgroundImageLayout = ImageLayout.Zoom;
            btn_fechar_visualizarprofessores.FlatAppearance.BorderSize = 0;
            btn_fechar_visualizarprofessores.FlatStyle = FlatStyle.Flat;
            btn_fechar_visualizarprofessores.Location = new Point(1234, 18);
            btn_fechar_visualizarprofessores.Name = "btn_fechar_visualizarprofessores";
            btn_fechar_visualizarprofessores.Size = new Size(34, 21);
            btn_fechar_visualizarprofessores.TabIndex = 20;
            btn_fechar_visualizarprofessores.UseVisualStyleBackColor = false;
            btn_fechar_visualizarprofessores.Click += btn_fechar_visualizarprofessores_Click;
            // 
            // lbl_professores
            // 
            lbl_professores.AutoSize = true;
            lbl_professores.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_professores.ForeColor = Color.MidnightBlue;
            lbl_professores.Location = new Point(616, 101);
            lbl_professores.Name = "lbl_professores";
            lbl_professores.Size = new Size(115, 25);
            lbl_professores.TabIndex = 30;
            lbl_professores.Text = "Professores";
            // 
            // btn_editar_visualizarprofessores
            // 
            btn_editar_visualizarprofessores.BackColor = Color.Transparent;
            btn_editar_visualizarprofessores.BackgroundImage = (Image)resources.GetObject("btn_editar_visualizarprofessores.BackgroundImage");
            btn_editar_visualizarprofessores.BackgroundImageLayout = ImageLayout.Zoom;
            btn_editar_visualizarprofessores.FlatAppearance.BorderSize = 0;
            btn_editar_visualizarprofessores.FlatStyle = FlatStyle.Flat;
            btn_editar_visualizarprofessores.ForeColor = SystemColors.ControlLightLight;
            btn_editar_visualizarprofessores.Location = new Point(994, 650);
            btn_editar_visualizarprofessores.Name = "btn_editar_visualizarprofessores";
            btn_editar_visualizarprofessores.Size = new Size(125, 33);
            btn_editar_visualizarprofessores.TabIndex = 31;
            btn_editar_visualizarprofessores.Text = "Editar";
            btn_editar_visualizarprofessores.UseVisualStyleBackColor = false;
            btn_editar_visualizarprofessores.Click += btn_editarprofessores_Click;
            // 
            // btn_salvar_visualizarprofessores
            // 
            btn_salvar_visualizarprofessores.BackColor = Color.Transparent;
            btn_salvar_visualizarprofessores.BackgroundImage = (Image)resources.GetObject("btn_salvar_visualizarprofessores.BackgroundImage");
            btn_salvar_visualizarprofessores.BackgroundImageLayout = ImageLayout.Zoom;
            btn_salvar_visualizarprofessores.FlatAppearance.BorderSize = 0;
            btn_salvar_visualizarprofessores.FlatStyle = FlatStyle.Flat;
            btn_salvar_visualizarprofessores.ForeColor = SystemColors.ControlLightLight;
            btn_salvar_visualizarprofessores.Location = new Point(718, 650);
            btn_salvar_visualizarprofessores.Name = "btn_salvar_visualizarprofessores";
            btn_salvar_visualizarprofessores.Size = new Size(125, 33);
            btn_salvar_visualizarprofessores.TabIndex = 36;
            btn_salvar_visualizarprofessores.Text = "Salvar";
            btn_salvar_visualizarprofessores.UseVisualStyleBackColor = false;
            btn_salvar_visualizarprofessores.Click += btn_salvarprofessores_Click;
            // 
            // lbl_paginacao_visualizarprofessores
            // 
            lbl_paginacao_visualizarprofessores.AutoSize = true;
            lbl_paginacao_visualizarprofessores.Font = new Font("Segoe UI", 10F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_paginacao_visualizarprofessores.ForeColor = Color.MidnightBlue;
            lbl_paginacao_visualizarprofessores.Location = new Point(291, 656);
            lbl_paginacao_visualizarprofessores.Name = "lbl_paginacao_visualizarprofessores";
            lbl_paginacao_visualizarprofessores.Size = new Size(100, 19);
            lbl_paginacao_visualizarprofessores.TabIndex = 39;
            lbl_paginacao_visualizarprofessores.Text = "Página 1 de 1";
            lbl_paginacao_visualizarprofessores.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // btn_proximo_visualizarprofessores
            // 
            btn_proximo_visualizarprofessores.BackColor = Color.Transparent;
            btn_proximo_visualizarprofessores.BackgroundImage = (Image)resources.GetObject("btn_proximo_visualizarprofessores.BackgroundImage");
            btn_proximo_visualizarprofessores.BackgroundImageLayout = ImageLayout.Zoom;
            btn_proximo_visualizarprofessores.FlatAppearance.BorderSize = 0;
            btn_proximo_visualizarprofessores.FlatStyle = FlatStyle.Flat;
            btn_proximo_visualizarprofessores.ForeColor = Color.White;
            btn_proximo_visualizarprofessores.Location = new Point(397, 653);
            btn_proximo_visualizarprofessores.Name = "btn_proximo_visualizarprofessores";
            btn_proximo_visualizarprofessores.Size = new Size(37, 26);
            btn_proximo_visualizarprofessores.TabIndex = 38;
            btn_proximo_visualizarprofessores.UseVisualStyleBackColor = false;
            btn_proximo_visualizarprofessores.Click += btn_proximo_paginacao_Click;
            // 
            // btn_anterior_visualizarprofessores
            // 
            btn_anterior_visualizarprofessores.BackColor = Color.Transparent;
            btn_anterior_visualizarprofessores.BackgroundImage = (Image)resources.GetObject("btn_anterior_visualizarprofessores.BackgroundImage");
            btn_anterior_visualizarprofessores.BackgroundImageLayout = ImageLayout.Zoom;
            btn_anterior_visualizarprofessores.FlatAppearance.BorderSize = 0;
            btn_anterior_visualizarprofessores.FlatStyle = FlatStyle.Flat;
            btn_anterior_visualizarprofessores.ForeColor = Color.White;
            btn_anterior_visualizarprofessores.Location = new Point(248, 653);
            btn_anterior_visualizarprofessores.Name = "btn_anterior_visualizarprofessores";
            btn_anterior_visualizarprofessores.Size = new Size(37, 26);
            btn_anterior_visualizarprofessores.TabIndex = 37;
            btn_anterior_visualizarprofessores.UseVisualStyleBackColor = false;
            // 
            // btn_excluir_disciplinas
            // 
            btn_excluir_disciplinas.BackColor = Color.Transparent;
            btn_excluir_disciplinas.BackgroundImage = (Image)resources.GetObject("btn_excluir_disciplinas.BackgroundImage");
            btn_excluir_disciplinas.BackgroundImageLayout = ImageLayout.Zoom;
            btn_excluir_disciplinas.FlatAppearance.BorderSize = 0;
            btn_excluir_disciplinas.FlatStyle = FlatStyle.Flat;
            btn_excluir_disciplinas.ForeColor = SystemColors.ControlLightLight;
            btn_excluir_disciplinas.Location = new Point(855, 650);
            btn_excluir_disciplinas.Name = "btn_excluir_disciplinas";
            btn_excluir_disciplinas.Size = new Size(125, 33);
            btn_excluir_disciplinas.TabIndex = 40;
            btn_excluir_disciplinas.Text = "Excluir";
            btn_excluir_disciplinas.UseVisualStyleBackColor = false;
            // 
            // C_VisualizarProfessores
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1280, 702);
            Controls.Add(btn_excluir_disciplinas);
            Controls.Add(lbl_paginacao_visualizarprofessores);
            Controls.Add(btn_proximo_visualizarprofessores);
            Controls.Add(btn_anterior_visualizarprofessores);
            Controls.Add(btn_salvar_visualizarprofessores);
            Controls.Add(btn_editar_visualizarprofessores);
            Controls.Add(lbl_professores);
            Controls.Add(chb_linha9_visualizarprofessores);
            Controls.Add(chb_linha8_visualizarprofessores);
            Controls.Add(chb_linha7_visualizarprofessores);
            Controls.Add(chb_linha6_visualizarprofessores);
            Controls.Add(chb_linha5_visualizarprofessores);
            Controls.Add(chb_linha4_visualizarprofessores);
            Controls.Add(chb_linha3_visualizarprofessores);
            Controls.Add(chb_linha2_visualizarprofessores);
            Controls.Add(chb_linha1_visualizarprofessores);
            Controls.Add(pnl_header);
            Controls.Add(pnl_professores);
            FormBorderStyle = FormBorderStyle.None;
            Name = "C_VisualizarProfessores";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "C_VisualizarProfessores";
            pnl_professores.ResumeLayout(false);
            pnl_professores.PerformLayout();
            pnl_header.ResumeLayout(false);
            pnl_header.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
        private TableLayoutPanel pnl_professores;
        private Label lbl_disciplina;
        private Label lbl_curso;
        private Label lbl_nome;
        private Panel pnl_header;
        private Button btn_voltar_visualizarprofessores;
        private Button btn_minimizar_visualizarprofessores;
        private Button btn_maximizar_visualizarprofessores;
        private Label lbl_titulo;
        private Button btn_fechar_visualizarprofessores;
        private Label lbl_professores;
        private Button btn_editar_visualizarprofessores;
        private Button btn_salvar_visualizarprofessores;
        private CheckBox chb_linha1_visualizarprofessores;
        private CheckBox chb_linha2_visualizarprofessores;
        private CheckBox chb_linha3_visualizarprofessores;
        private CheckBox chb_linha4_visualizarprofessores;
        private CheckBox chb_linha5_visualizarprofessores;
        private CheckBox chb_linha6_visualizarprofessores;
        private CheckBox chb_linha7_visualizarprofessores;
        private CheckBox chb_linha8_visualizarprofessores;
        private CheckBox chb_linha9_visualizarprofessores;
        private Label lbl_paginacao_visualizarprofessores;
        private Button btn_proximo_visualizarprofessores;
        private Button btn_anterior_visualizarprofessores;
        private Button btn_excluir_disciplinas;
    }
}