-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Tempo de geração: 24/05/2025 às 21:07
-- Vers<PERSON> do servidor: 10.4.32-MariaDB
-- Versão do PHP: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- <PERSON><PERSON> de dados: `sehd`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON><PERSON> para tabela `aulas`
--

CREATE TABLE `aulas` (
  `ID_AULAS` int(11) NOT NULL,
  `AULAS_SEMANAIS` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `aulas`
--

INSERT INTO `aulas` (`ID_AULAS`, `AULAS_SEMANAIS`) VALUES
(125, 'Introdução ao HTML e CSS'),
(126, 'JavaScript: Conceitos Básicos'),
(127, 'Funções e Eventos em JavaScript'),
(128, 'Manipulação de DOM com JavaScript'),
(129, 'Projeto Final: Desenvolvimento de uma Página Web');

-- --------------------------------------------------------

--
-- Estrutura para tabela `coordenador`
--

CREATE TABLE `coordenador` (
  `ID_COORDENADOR` int(11) NOT NULL,
  `RA` int(11) DEFAULT NULL,
  `NOME_COORDENADOR` varchar(100) DEFAULT NULL,
  `data_nasc_coord` varchar(32) DEFAULT NULL,
  `ID_CURSO` int(11) DEFAULT NULL,
  `ID_DISPONIBILIDADE` int(11) DEFAULT NULL,
  `ID_DISCIPLINA` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `coordenador`
--

INSERT INTO `coordenador` (`ID_COORDENADOR`, `RA`, `NOME_COORDENADOR`, `data_nasc_coord`, `ID_CURSO`, `ID_DISPONIBILIDADE`, `ID_DISCIPLINA`) VALUES
(1, 12345, 'Jenni', '186a8bab269a3e64e01767b76beba61e', 1, 1, 17),
(6, 3022106, 'Rafael Almeida', 'b614e537987bbba6361270ab4cb935b6', 1, 1, 17),
(7, 3022107, 'Marina Costa', 'edb20917739d6268cc8f324883704ba9', 2, 2, 18),
(8, 3022108, 'Lucas Fernandes', '029e508b4efdde00682f3aafc8f59be0', 3, 3, 19);

-- --------------------------------------------------------

--
-- Estrutura para tabela `curso`
--

CREATE TABLE `curso` (
  `ID_CURSO` int(11) NOT NULL,
  `NOME_CURSO` varchar(100) DEFAULT NULL,
  `QTD_AULAS` int(11) DEFAULT NULL,
  `INICIOPERIODO` date DEFAULT NULL,
  `FIMPERIODO` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `curso`
--

INSERT INTO `curso` (`ID_CURSO`, `NOME_CURSO`, `QTD_AULAS`, `INICIOPERIODO`, `FIMPERIODO`) VALUES
(1, 'Desenvolvimento Web com JavaScript', 40, '2025-06-01', '2025-08-15'),
(2, 'Introdução à Cibersegurança', 30, '2025-07-01', '2025-09-01'),
(3, 'Banco de Dados com MySQL', 35, '2025-06-10', '2025-08-20'),
(4, 'Redes de Computadores e Protocolos', 45, '2025-07-05', '2025-09-25'),
(5, 'Análise de Dados com Python', 40, '2025-08-01', '2025-10-15');

-- --------------------------------------------------------

--
-- Estrutura para tabela `diretor`
--

CREATE TABLE `diretor` (
  `ID_DIRETOR` int(11) NOT NULL,
  `RA` int(11) DEFAULT NULL,
  `NOME_DIRETOR` varchar(100) DEFAULT NULL,
  `ID_CURSO` int(11) DEFAULT NULL,
  `data_nasc_diretor` varchar(32) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `diretor`
--

INSERT INTO `diretor` (`ID_DIRETOR`, `RA`, `NOME_DIRETOR`, `ID_CURSO`, `data_nasc_diretor`) VALUES
(1, 666, 'Chifronesio', 1, 'c4da8783a949a532e1651e5ba13dc75b');

-- --------------------------------------------------------

--
-- Estrutura para tabela `disciplina`
--

CREATE TABLE `disciplina` (
  `ID_DISCIPLINA` int(11) NOT NULL,
  `NOME_DISCIPLINA` varchar(100) DEFAULT NULL,
  `CARGA_HORARIA` int(11) DEFAULT NULL,
  `ID_CURSO` int(11) DEFAULT NULL,
  `ID_AULAS` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `disciplina`
--

INSERT INTO `disciplina` (`ID_DISCIPLINA`, `NOME_DISCIPLINA`, `CARGA_HORARIA`, `ID_CURSO`, `ID_AULAS`) VALUES
(0, 'jennifer', 360, 2, NULL),
(17, 'Introdução ao Python', 1, 1, 125),
(18, 'Bibliotecas NumPy e Pandas', 2, 2, 126),
(19, 'Visualização de Dados', 3, 3, 127),
(20, 'Projeto Final com Python', 4, 4, 128);

-- --------------------------------------------------------

--
-- Estrutura para tabela `disponibilidade`
--

CREATE TABLE `disponibilidade` (
  `ID_DISPONIBILIDADE` int(11) NOT NULL,
  `HORARIO_INICIO` time DEFAULT NULL,
  `HORARIO_FIM` time DEFAULT NULL,
  `DIA_SEMANA` varchar(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `disponibilidade`
--

INSERT INTO `disponibilidade` (`ID_DISPONIBILIDADE`, `HORARIO_INICIO`, `HORARIO_FIM`, `DIA_SEMANA`) VALUES
(1, '19:00:00', '23:30:00', 'Segunda-feira'),
(2, '19:00:00', '23:30:00', 'Terça-feira'),
(3, '19:00:00', '23:30:00', 'Quarta-feira'),
(4, '19:00:00', '23:30:00', 'Quinta-feira'),
(5, '19:00:00', '23:30:00', 'Sexta-feira');

-- --------------------------------------------------------

--
-- Estrutura para tabela `login`
--

CREATE TABLE `login` (
  `ID_LOGIN` int(11) NOT NULL,
  `RA` int(11) NOT NULL,
  `senha` varchar(40) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `login`
--

INSERT INTO `login` (`ID_LOGIN`, `RA`, `senha`) VALUES
(1, 12345678, 'aba2fbf2a2eed491f28c5a2655a06f50');

-- --------------------------------------------------------

--
-- Estrutura para tabela `matriz`
--

CREATE TABLE `matriz` (
  `ID_MATRIZ` int(11) NOT NULL,
  `ID_AULAS` int(11) DEFAULT NULL,
  `SEMESTRE` int(11) DEFAULT NULL,
  `HORA_ANO` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `professor`
--

CREATE TABLE `professor` (
  `ID_PROFESSOR` int(11) NOT NULL,
  `NOME_PROFESSOR` varchar(100) DEFAULT NULL,
  `data_nasc_prof` varchar(32) DEFAULT NULL,
  `RA` int(11) DEFAULT NULL,
  `ID_CURSO` int(11) DEFAULT NULL,
  `ID_DISPONIBILIDADE` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `professor`
--

INSERT INTO `professor` (`ID_PROFESSOR`, `NOME_PROFESSOR`, `data_nasc_prof`, `RA`, `ID_CURSO`, `ID_DISPONIBILIDADE`) VALUES
(1, 'Ana Beatriz Martins', 'cc89ae86f8997ee59a77a4d3e42f8270', 2023101, 1, 1),
(2, 'Carlos Henrique Souza', '1b7b5b68cc0763ef71ef995119cdf4e3', 2023102, 2, 2),
(3, 'Fernanda Lopes da Silva', '1502d4f820259de9bf8fba4089893235', 2023103, 3, 3),
(4, 'João Paulo Andrade', '8fa5b98d9340b873e96a9674cf8222ba', 2023104, 4, 4),
(5, 'Juliana Ribeiro Costa', 'e25d81683940fe9c06a61cee4da62e6a', 2023105, 1, 5);

--
-- Índices para tabelas despejadas
--

--
-- Índices de tabela `aulas`
--
ALTER TABLE `aulas`
  ADD PRIMARY KEY (`ID_AULAS`);

--
-- Índices de tabela `coordenador`
--
ALTER TABLE `coordenador`
  ADD PRIMARY KEY (`ID_COORDENADOR`),
  ADD KEY `ID_CURSO` (`ID_CURSO`),
  ADD KEY `ID_DISCIPLINA` (`ID_DISCIPLINA`),
  ADD KEY `ID_DISPONIBILIDADE` (`ID_DISPONIBILIDADE`);

--
-- Índices de tabela `curso`
--
ALTER TABLE `curso`
  ADD PRIMARY KEY (`ID_CURSO`);

--
-- Índices de tabela `diretor`
--
ALTER TABLE `diretor`
  ADD PRIMARY KEY (`ID_DIRETOR`),
  ADD KEY `ID_CURSO` (`ID_CURSO`);

--
-- Índices de tabela `disciplina`
--
ALTER TABLE `disciplina`
  ADD PRIMARY KEY (`ID_DISCIPLINA`),
  ADD KEY `ID_CURSO` (`ID_CURSO`),
  ADD KEY `ID_AULAS` (`ID_AULAS`);

--
-- Índices de tabela `disponibilidade`
--
ALTER TABLE `disponibilidade`
  ADD PRIMARY KEY (`ID_DISPONIBILIDADE`);

--
-- Índices de tabela `login`
--
ALTER TABLE `login`
  ADD PRIMARY KEY (`ID_LOGIN`);

--
-- Índices de tabela `matriz`
--
ALTER TABLE `matriz`
  ADD PRIMARY KEY (`ID_MATRIZ`),
  ADD KEY `ID_AULAS` (`ID_AULAS`);

--
-- Índices de tabela `professor`
--
ALTER TABLE `professor`
  ADD PRIMARY KEY (`ID_PROFESSOR`),
  ADD KEY `ID_CURSO` (`ID_CURSO`),
  ADD KEY `ID_DISPONIBILIDADE` (`ID_DISPONIBILIDADE`);

--
-- Restrições para tabelas despejadas
--

--
-- Restrições para tabelas `coordenador`
--
ALTER TABLE `coordenador`
  ADD CONSTRAINT `coordenador_ibfk_1` FOREIGN KEY (`ID_CURSO`) REFERENCES `curso` (`ID_CURSO`),
  ADD CONSTRAINT `coordenador_ibfk_2` FOREIGN KEY (`ID_DISCIPLINA`) REFERENCES `disciplina` (`ID_DISCIPLINA`),
  ADD CONSTRAINT `coordenador_ibfk_3` FOREIGN KEY (`ID_DISPONIBILIDADE`) REFERENCES `disponibilidade` (`ID_DISPONIBILIDADE`);

--
-- Restrições para tabelas `diretor`
--
ALTER TABLE `diretor`
  ADD CONSTRAINT `diretor_ibfk_1` FOREIGN KEY (`ID_CURSO`) REFERENCES `curso` (`ID_CURSO`);

--
-- Restrições para tabelas `disciplina`
--
ALTER TABLE `disciplina`
  ADD CONSTRAINT `disciplina_ibfk_1` FOREIGN KEY (`ID_CURSO`) REFERENCES `curso` (`ID_CURSO`),
  ADD CONSTRAINT `disciplina_ibfk_2` FOREIGN KEY (`ID_AULAS`) REFERENCES `aulas` (`ID_AULAS`);

--
-- Restrições para tabelas `matriz`
--
ALTER TABLE `matriz`
  ADD CONSTRAINT `matriz_ibfk_1` FOREIGN KEY (`ID_AULAS`) REFERENCES `aulas` (`ID_AULAS`);

--
-- Restrições para tabelas `professor`
--
ALTER TABLE `professor`
  ADD CONSTRAINT `professor_ibfk_1` FOREIGN KEY (`ID_CURSO`) REFERENCES `curso` (`ID_CURSO`),
  ADD CONSTRAINT `professor_ibfk_2` FOREIGN KEY (`ID_DISPONIBILIDADE`) REFERENCES `disponibilidade` (`ID_DISPONIBILIDADE`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
