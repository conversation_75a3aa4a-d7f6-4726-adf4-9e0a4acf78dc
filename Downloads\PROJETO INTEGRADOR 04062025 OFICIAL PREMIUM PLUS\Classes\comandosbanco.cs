﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MySqlX.XDevAPI;
using ProjetoIntegrador.conn;
using MySql.Data.MySqlClient;

namespace ProjetoIntegrador.Classes
{
    internal class comandosbanco
    {
        public ConexaoBanco SQL = new ConexaoBanco();

        // FUNÇÕES PARA INSERÇÃO DE DADOS NO BANCO DE DADOS SEHD:

        // CADASTRAR DISCIPLINAS NA TABELA DISCIPLINA
        public void CadastroDisciplina(variaveisbanco Modelo)
        {
            // Monta a query SQL para inserir uma nova disciplina
            // Parâmetros: nome da disciplina e carga horária
            string Query = "INSERT INTO DISCIPLINA (NOME_DISCIPLINA, CARGA_HORARIA, ID_CURSO, ID_AULAS) " +
                           "VALUES ('" + Modelo.nomedisciplina + "', " + Modelo.cargahoraria + ", " +
                           "(SELECT ID_CURSO FROM CURSO WHERE NOME_CURSO = '" + Modelo.nomecurso + "'), " +
                           "(SELECT ID_AULAS FROM AULAS WHERE AULAS_SEMANAIS = '" + Modelo.quantidadeaulas + "'))";

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // CADASTRAR PROFESSORES NA TABELA PROFESSOR
        public void CadastroProfessor(variaveisbanco Modelo)
        {
            // Buscar o próximo ID disponível para professor
            string queryMaxId = "SELECT COALESCE(MAX(ID_PROFESSOR), 0) + 1 AS PROXIMO_ID FROM PROFESSOR";
            var resultado = SQL.CarregaDados(queryMaxId);
            int proximoId = Convert.ToInt32(resultado.Rows[0]["PROXIMO_ID"]);

            // Monta a query SQL para inserir um novo professor com ID específico
            // Parâmetros: ID, nome do professor, data de nascimento e RA
            string Query = "INSERT INTO PROFESSOR (ID_PROFESSOR, NOME_PROFESSOR, data_nasc_prof, RA, ID_CURSO) " +
                           "VALUES (" + proximoId + ", '" + Modelo.nomeprofessor + "', '" + Modelo.datanascimentoprofessor + "', " +
                           Modelo.raprofessor + ", " +
                           "(SELECT ID_CURSO FROM CURSO WHERE NOME_CURSO = '" + Modelo.nomecurso + "'))";

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // CADASTRAR COORDENADORES NA TABELA COORDENADOR
        public void CadastroCoordenador(variaveisbanco Modelo)
        {
            // Monta a query SQL para inserir um novo coordenador
            // Parâmetros: RA, nome do coordenador e data de nascimento
            string Query = "INSERT INTO COORDENADOR (RA, NOME_COORDENADOR, DATA_NASC_COORD, ID_CURSO) " +
                           "VALUES (" + Modelo.racoordenador + ", '" + Modelo.nomecoordenador + "', '" +
                           Modelo.datanascimentocoordenador + "', " +
                           "(SELECT ID_CURSO FROM CURSO WHERE NOME_CURSO = '" + Modelo.nomecurso + "'))";

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // CADASTRAR CURSOS NA TABELA CURSO
        public void CadastroCurso(variaveisbanco Modelo)
        {
            // Monta a query SQL para inserir um novo curso
            // Parâmetros: nome do curso, quantidade de aulas, data de início e fim do período
            string Query = "INSERT INTO CURSO (NOME_CURSO, QTD_AULAS, INICIOPERIODO, FIMPERIODO) " +
                           "VALUES ('" + Modelo.nomecurso + "', '" + Modelo.quantidadeaulas + "', '" +
                           Modelo.inicioperiodo.ToString("yyyy-MM-dd") + "', '" +
                           Modelo.fimperiodo.ToString("yyyy-MM-dd") + "')";

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // CADASTRAR DISPONIBILIDADES NA TABELA DISPONIBILIDADE
        public void CadastroDisponibilidade(variaveisbanco Modelo)
        {
            // Monta a query SQL para inserir uma nova disponibilidade
            // Parâmetros: horário de início, horário de fim e dia da semana
            string Query = "INSERT INTO DISPONIBILIDADE (HORARIO_INICIO, HORARIO_FIM, DIA_SEMANA) " +
                           "VALUES ('" + Modelo.horarioinicio.ToString("HH:mm:ss") + "', '" +
                           Modelo.horariofim.ToString("HH:mm:ss") + "', '" + Modelo.diasemana + "')";

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // INSERIR DISPONIBILIDADE COM TRATAMENTO DE ERROS E RETORNO BOOLEAN
        public bool InserirDisponibilidade(variaveisbanco dados)
        {
            try
            {
                // Validar dados de entrada
                if (dados == null)
                {
                    throw new ArgumentNullException(nameof(dados), "Os dados não podem ser nulos");
                }

                if (string.IsNullOrWhiteSpace(dados.diasemana))
                {
                    throw new ArgumentException("Dia da semana é obrigatório", nameof(dados.diasemana));
                }

                // Escape de aspas simples para evitar SQL injection
                string diaSemanaEscapado = dados.diasemana.Replace("'", "''");

                // Monta a query SQL com parâmetros seguros
                string Query = "INSERT INTO DISPONIBILIDADE (HORARIO_INICIO, HORARIO_FIM, DIA_SEMANA) " +
                               "VALUES ('" + dados.horarioinicio.ToString("HH:mm:ss") + "', '" +
                               dados.horariofim.ToString("HH:mm:ss") + "', '" + diaSemanaEscapado + "')";

                // Executa a query no banco de dados
                SQL.ManipulaDados(Query);

                return true; // Sucesso
            }
            catch (Exception ex)
            {
                // Log do erro para debug (você pode implementar um sistema de log mais robusto)
                System.Diagnostics.Debug.WriteLine($"Erro ao inserir disponibilidade: {ex.Message}");

                // Retorna false em caso de erro
                return false;
            }
        }

        // CADASTRAR AULAS NA TABELA AULAS
        public void CadastroAulas(variaveisbanco Modelo)
        {
            // Monta a query SQL para inserir uma nova entrada na tabela aulas
            // Parâmetros: quantidade de aulas semanais
            string Query = "INSERT INTO AULAS (AULAS_SEMANAIS) VALUES ('" + Modelo.quantidadeaulas + "')";

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // CADASTRAR MATRIZ CURRICULAR NA TABELA MATRIZ
        public void CadastroMatriz(variaveisbanco Modelo)
        {
            // Monta a query SQL para inserir uma nova matriz curricular
            // Parâmetros: ID_AULAS, semestre e horas por ano
            string Query = "INSERT INTO MATRIZ (ID_AULAS, SEMESTRE, HORA_ANO) " +
                           "VALUES ((SELECT ID_AULAS FROM AULAS WHERE AULAS_SEMANAIS = '" + Modelo.quantidadeaulas + "'), " +
                           Modelo.semestre + ", " + Modelo.horaano + ")";

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // FUNÇÕES PARA DELETAR DADOS NO BANCO DE DADOS SEHD:

        // DELETAR DISCIPLINA DA TABELA DISCIPLINA
        public void DeletarDisciplina(int idDisciplina)
        {
            // Monta a query SQL para deletar uma disciplina pelo ID
            string Query = "DELETE FROM DISCIPLINA WHERE ID_DISCIPLINA = " + idDisciplina;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // DELETAR PROFESSOR DA TABELA PROFESSOR
        public void DeletarProfessor(int idProfessor)
        {
            // Monta a query SQL para deletar um professor pelo ID
            string Query = "DELETE FROM PROFESSOR WHERE ID_PROFESSOR = " + idProfessor;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // DELETAR COORDENADOR DA TABELA COORDENADOR
        public void DeletarCoordenador(int idCoordenador)
        {
            // Monta a query SQL para deletar um coordenador pelo ID
            string Query = "DELETE FROM COORDENADOR WHERE ID_COORDENADOR = " + idCoordenador;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // DELETAR CURSO DA TABELA CURSO
        public void DeletarCurso(int idCurso)
        {
            // Monta a query SQL para deletar um curso pelo ID
            string Query = "DELETE FROM CURSO WHERE ID_CURSO = " + idCurso;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // DELETAR DISPONIBILIDADE DA TABELA DISPONIBILIDADE
        public void DeletarDisponibilidade(int idDisponibilidade)
        {
            string query = "DELETE FROM disponibilidade WHERE ID_DISPONIBILIDADE = " + idDisponibilidade;
            SQL.ManipulaDados(query);
        }




        public DataTable BuscarTodasDisponibilidades()
        {
            string query = "SELECT * FROM disponibilidade";
            return SQL.BuscarDados(query);
        }




        // DELETAR AULAS DA TABELA AULAS
        public void DeletarAulas(int idAulas)
        {
            // Monta a query SQL para deletar uma entrada da tabela aulas pelo ID
            string Query = "DELETE FROM AULAS WHERE ID_AULAS = " + idAulas;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // DELETAR MATRIZ DA TABELA MATRIZ
        public void DeletarMatriz(int idMatriz)
        {
            // Monta a query SQL para deletar uma matriz curricular pelo ID
            string Query = "DELETE FROM MATRIZ WHERE ID_MATRIZ = " + idMatriz;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // FUNÇÕES PARA ALTERAÇÃO DADOS NO BANCO DE DADOS SEHD:

        // ATUALIZAR DISCIPLINA NA TABELA DISCIPLINA
        public void AtualizarDisciplina(variaveisbanco Modelo, int idDisciplina)
        {
            // Monta a query SQL para atualizar uma disciplina pelo ID
            string Query = "UPDATE DISCIPLINA SET NOME_DISCIPLINA = '" + Modelo.nomedisciplina + "', " +
                           "CARGA_HORARIA = " + Modelo.cargahoraria + ", " +
                           "ID_CURSO = (SELECT ID_CURSO FROM CURSO WHERE NOME_CURSO = '" + Modelo.nomecurso + "'), " +
                           "ID_AULAS = (SELECT ID_AULAS FROM AULAS WHERE AULAS_SEMANAIS = '" + Modelo.quantidadeaulas + "') " +
                           "WHERE ID_DISCIPLINA = " + idDisciplina;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // ATUALIZAR PROFESSOR NA TABELA PROFESSOR
        public void AtualizarProfessor(variaveisbanco Modelo, int idProfessor)
        {
            // Monta a query SQL para atualizar um professor pelo ID
            string Query = "UPDATE PROFESSOR SET NOME_PROFESSOR = '" + Modelo.nomeprofessor + "', " +
                           "data_nasc_prof = '" + Modelo.datanascimentoprofessor + "', " +
                           "RA = " + Modelo.raprofessor + ", " +
                           "ID_CURSO = (SELECT ID_CURSO FROM CURSO WHERE NOME_CURSO = '" + Modelo.nomecurso + "') " +
                           "WHERE ID_PROFESSOR = " + idProfessor;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // ATUALIZAR COORDENADOR NA TABELA COORDENADOR
        public void AtualizarCoordenador(variaveisbanco Modelo, int idCoordenador)
        {
            // Monta a query SQL para atualizar um coordenador pelo ID
            string Query = "UPDATE COORDENADOR SET RA = " + Modelo.racoordenador + ", " +
                           "NOME_COORDENADOR = '" + Modelo.nomecoordenador + "', " +
                           "DATA_NASC_COORD = '" + Modelo.datanascimentocoordenador + "', " +
                           "ID_CURSO = (SELECT ID_CURSO FROM CURSO WHERE NOME_CURSO = '" + Modelo.nomecurso + "') " +
                           "WHERE ID_COORDENADOR = " + idCoordenador;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // ATUALIZAR CURSO NA TABELA CURSO
        public void AtualizarCurso(variaveisbanco Modelo, int idCurso)
        {
            // Monta a query SQL para atualizar um curso pelo ID
            string Query = "UPDATE CURSO SET NOME_CURSO = '" + Modelo.nomecurso + "', " +
                           "QTD_AULAS = '" + Modelo.quantidadeaulas + "', " +
                           "INICIOPERIODO = '" + Modelo.inicioperiodo.ToString("yyyy-MM-dd") + "', " +
                           "FIMPERIODO = '" + Modelo.fimperiodo.ToString("yyyy-MM-dd") + "' " +
                           "WHERE ID_CURSO = " + idCurso;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // ATUALIZAR DISPONIBILIDADE NA TABELA DISPONIBILIDADE
        public void AtualizarDisponibilidade(variaveisbanco Modelo, int idDisponibilidade)
        {
            // Monta a query SQL para atualizar uma disponibilidade pelo ID
            string Query = "UPDATE DISPONIBILIDADE SET HORARIO_INICIO = '" + Modelo.horarioinicio.ToString("HH:mm:ss") + "', " +
                           "HORARIO_FIM = '" + Modelo.horariofim.ToString("HH:mm:ss") + "', " +
                           "DIA_SEMANA = '" + Modelo.diasemana + "' " +
                           "WHERE ID_DISPONIBILIDADE = " + idDisponibilidade;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // FUNÇÕES PARA CONSULTA DE DADOS NO BANCO DE DADOS SEHD:

        // CONSULTAR TODOS OS CURSOS
        public DataTable ConsultarCursos()
        {
            // Monta a query SQL para consultar todos os cursos
            string Query = "SELECT * FROM CURSO ORDER BY NOME_CURSO";

            // Executa a consulta e retorna os resultados
            return SQL.CarregaDados(Query);
        }

        // CONSULTAR TODAS AS DISCIPLINAS
        public DataTable ConsultarDisciplinas()
        {
            // Monta a query SQL para consultar todas as disciplinas com informações do curso
            string Query = "SELECT d.ID_DISCIPLINA, d.NOME_DISCIPLINA, d.CARGA_HORARIA, c.NOME_CURSO, a.AULAS_SEMANAIS " +
                           "FROM DISCIPLINA d " +
                           "LEFT JOIN CURSO c ON d.ID_CURSO = c.ID_CURSO " +
                           "LEFT JOIN AULAS a ON d.ID_AULAS = a.ID_AULAS " +
                           "ORDER BY d.NOME_DISCIPLINA";

            // Executa a consulta e retorna os resultados
            return SQL.CarregaDados(Query);
        }

        // CONSULTAR TODOS OS PROFESSORES
        public DataTable ConsultarProfessores()
        {
            // Monta a query SQL para consultar todos os professores com informações do curso
            string Query = "SELECT p.ID_PROFESSOR, p.NOME_PROFESSOR, p.data_nasc_prof, p.RA, c.NOME_CURSO " +
                           "FROM PROFESSOR p " +
                           "LEFT JOIN CURSO c ON p.ID_CURSO = c.ID_CURSO " +
                           "ORDER BY p.NOME_PROFESSOR";

            // Executa a consulta e retorna os resultados
            return SQL.CarregaDados(Query);
        }

        // CONSULTAR PROFESSORES COM DISCIPLINAS PARA VISUALIZAÇÃO
        public DataTable ConsultarProfessoresComDisciplinas()
        {
            // Monta a query SQL para consultar professores com suas disciplinas através do curso
            string Query = "SELECT p.ID_PROFESSOR, p.NOME_PROFESSOR, c.NOME_CURSO, " +
                           "GROUP_CONCAT(DISTINCT d.NOME_DISCIPLINA SEPARATOR ', ') AS NOME_DISCIPLINA " +
                           "FROM PROFESSOR p " +
                           "LEFT JOIN CURSO c ON p.ID_CURSO = c.ID_CURSO " +
                           "LEFT JOIN DISCIPLINA d ON c.ID_CURSO = d.ID_CURSO " +
                           "GROUP BY p.ID_PROFESSOR, p.NOME_PROFESSOR, c.NOME_CURSO " +
                           "ORDER BY p.NOME_PROFESSOR";

            // Executa a consulta e retorna os resultados
            return SQL.CarregaDados(Query);
        }

        // CONSULTAR TODAS AS DISPONIBILIDADES
        public DataTable ConsultarDisponibilidades()
        {
            
            string Query = "SELECT ID_DISPONIBILIDADE, DIA_SEMANA, HORARIO_INICIO, HORARIO_FIM " +
                           "FROM DISPONIBILIDADE " +
                           "ORDER BY FIELD(DIA_SEMANA, 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo'), HORARIO_INICIO";

            // Executa a consulta e retorna os resultados
            return SQL.CarregaDados(Query);
        }


        // CONSULTAR TODAS AS AULAS
        public DataTable ConsultarAulas()
        {
            // Monta a query SQL para consultar todas as aulas
            string Query = "SELECT * FROM AULAS ORDER BY AULAS_SEMANAIS";

            // Executa a consulta e retorna os resultados
            return SQL.CarregaDados(Query);
        }

        // CONSULTAR TODOS OS COORDENADORES
        public DataTable ConsultarCoordenadores()
        {
            // Monta a query SQL para consultar todos os coordenadores com informações do curso
            string Query = "SELECT c.ID_COORDENADOR, c.RA, c.NOME_COORDENADOR, c.data_nasc_coord, cu.NOME_CURSO " +
                           "FROM COORDENADOR c " +
                           "LEFT JOIN CURSO cu ON c.ID_CURSO = cu.ID_CURSO " +
                           "ORDER BY c.NOME_COORDENADOR";

            // Executa a consulta e retorna os resultados
            return SQL.CarregaDados(Query);
        }

        // CADASTRAR DISCIPLINA COM AULAS SEMANAIS
        public void CadastroDisciplinaSimplificado(variaveisbanco Modelo)
        {
            // Limpar e validar dados de entrada
            string nomeDisciplina = Modelo.nomedisciplina.Replace("'", "''"); // Escape aspas simples
            string nomeCurso = Modelo.nomecurso.Replace("'", "''"); // Escape aspas simples

            // Buscar o ID do registro de aulas (cria APENAS se não existir)
            int idAulas = GarantirRegistroAulas(Modelo.quantidadeaulas);

            // Buscar o próximo ID disponível para disciplina
            string queryMaxId = "SELECT COALESCE(MAX(ID_DISCIPLINA), 0) + 1 AS PROXIMO_ID FROM DISCIPLINA";
            var resultado = SQL.CarregaDados(queryMaxId);
            int proximoId = Convert.ToInt32(resultado.Rows[0]["PROXIMO_ID"]);

            // Monta a query SQL para inserir uma nova disciplina com ID específico e aulas
            string Query = "INSERT INTO DISCIPLINA (ID_DISCIPLINA, NOME_DISCIPLINA, CARGA_HORARIA, ID_CURSO, ID_AULAS) " +
                           "VALUES (" + proximoId + ", '" + nomeDisciplina + "', " + Modelo.cargahoraria + ", " +
                           "(SELECT ID_CURSO FROM CURSO WHERE NOME_CURSO = '" + nomeCurso + "'), " + idAulas + ")";

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        public void CadastroDisponibilidadeSimplificado(variaveisbanco Modelo)
        {
            // Limpar e validar dados de entrada (escape de aspas simples no dia da semana)
            string diaSemana = Modelo.diasemana.Replace("'", "''");

            // Buscar o próximo ID disponível para disponibilidade
            string queryMaxId = "SELECT COALESCE(MAX(ID_DISPONIBILIDADE), 0) + 1 AS PROXIMO_ID FROM DISPONIBILIDADE";
            var resultado = SQL.CarregaDados(queryMaxId);
            int proximoId = Convert.ToInt32(resultado.Rows[0]["PROXIMO_ID"]);

            // Monta a query SQL para inserir uma nova disponibilidade com ID específico
            string Query = "INSERT INTO DISPONIBILIDADE (ID_DISPONIBILIDADE, DIA_SEMANA, HORARIO_INICIO, HORARIO_FIM) " +
                           "VALUES (" + proximoId + ", '" + diaSemana + "', '" + Modelo.horarioinicio.ToString(@"hh\:mm\:ss") + "', '" + Modelo.horariofim.ToString(@"hh\:mm\:ss") + "')";

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }


        // Método auxiliar para garantir que existe um registro na tabela AULAS
        private int GarantirRegistroAulas(string quantidadeAulas)
        {
            // Verificar se já existe um registro para esta quantidade
            string queryVerificar = "SELECT ID_AULAS FROM AULAS WHERE AULAS_SEMANAIS = '" + quantidadeAulas + "'";
            var resultado = SQL.CarregaDados(queryVerificar);

            if (resultado.Rows.Count > 0)
            {
                // Já existe, retorna o ID existente
                return Convert.ToInt32(resultado.Rows[0]["ID_AULAS"]);
            }
            else
            {
                // Não existe, criar novo registro APENAS se necessário
                // Buscar próximo ID disponível para AULAS
                string queryMaxIdAulas = "SELECT COALESCE(MAX(ID_AULAS), 0) + 1 AS PROXIMO_ID FROM AULAS";
                var resultadoMax = SQL.CarregaDados(queryMaxIdAulas);
                int proximoIdAulas = Convert.ToInt32(resultadoMax.Rows[0]["PROXIMO_ID"]);

                // Inserir novo registro na tabela AULAS
                string queryInserir = "INSERT INTO AULAS (ID_AULAS, AULAS_SEMANAIS) VALUES (" + proximoIdAulas + ", '" + quantidadeAulas + "')";
                SQL.ManipulaDados(queryInserir);

                return proximoIdAulas;
            }
        }

        // Método alternativo mais eficiente - busca ou cria registros padrão
        private void CriarRegistrosAulasPadrao()
        {
            // Criar registros padrão para 1, 2, 3, 4, 5 se não existirem
            for (int i = 1; i <= 5; i++)
            {
                string queryVerificar = "SELECT COUNT(*) as EXISTE FROM AULAS WHERE AULAS_SEMANAIS = '" + i + "'";
                var resultado = SQL.CarregaDados(queryVerificar);
                int existe = Convert.ToInt32(resultado.Rows[0]["EXISTE"]);

                if (existe == 0)
                {
                    // Buscar próximo ID disponível
                    string queryMaxId = "SELECT COALESCE(MAX(ID_AULAS), 0) + 1 AS PROXIMO_ID FROM AULAS";
                    var resultadoMax = SQL.CarregaDados(queryMaxId);
                    int proximoId = Convert.ToInt32(resultadoMax.Rows[0]["PROXIMO_ID"]);

                    // Inserir registro
                    string queryInserir = "INSERT INTO AULAS (ID_AULAS, AULAS_SEMANAIS) VALUES (" + proximoId + ", '" + i + "')";
                    SQL.ManipulaDados(queryInserir);
                }
            }
        }

        // ATUALIZAR DISCIPLINA (versão simplificada para VisualizarDisciplinas)
        public void AtualizarDisciplina(int idDisciplina, string nomeDisciplina, int cargaHoraria, string aulasSemanais = null)
        {
            // Escape de aspas simples para evitar SQL injection
            string nomeDisciplinaEscapado = nomeDisciplina.Replace("'", "''");

            // Monta a query SQL para atualizar uma disciplina
            string Query = "UPDATE DISCIPLINA SET " +
                           "NOME_DISCIPLINA = '" + nomeDisciplinaEscapado + "', " +
                           "CARGA_HORARIA = " + cargaHoraria;

            // Se aulas semanais foi fornecido, atualizar também
            if (!string.IsNullOrEmpty(aulasSemanais))
            {
                // Buscar o ID do registro de aulas (cria APENAS se não existir)
                int idAulas = GarantirRegistroAulas(aulasSemanais);
                Query += ", ID_AULAS = " + idAulas;
            }

            Query += " WHERE ID_DISCIPLINA = " + idDisciplina;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        // ATUALIZAR PROFESSOR (versão simplificada para VisualizarProfessores)
        public void AtualizarProfessor(int idProfessor, string nomeProfessor, string nomeCurso)
        {
            // Escape de aspas simples para evitar SQL injection
            string nomeProfessorEscapado = nomeProfessor.Replace("'", "''");
            string nomeCursoEscapado = nomeCurso.Replace("'", "''");

            // Monta a query SQL para atualizar um professor
            string Query = "UPDATE PROFESSOR SET " +
                           "NOME_PROFESSOR = '" + nomeProfessorEscapado + "', " +
                           "ID_CURSO = (SELECT ID_CURSO FROM CURSO WHERE NOME_CURSO = '" + nomeCursoEscapado + "') " +
                           "WHERE ID_PROFESSOR = " + idProfessor;

            // Executa a query no banco de dados
            SQL.ManipulaDados(Query);
        }

        public DataTable ConsultarMatrizesCurriculares(int idCurso)
        {
            DataTable dtMatrizes = new DataTable();

            // Usar SELECT com JOIN para obter Disciplina, Aulas Semanais e Carga Horaria (como Hora Ano)
            string query = "SELECT d.NOME_DISCIPLINA AS Disciplina, a.AULAS_SEMANAIS AS AulasSemanais, d.CARGA_HORARIA AS HoraAno " +
                           "FROM DISCIPLINA d " +
                           "LEFT JOIN AULAS a ON d.ID_AULAS = a.ID_AULAS " +
                           "WHERE d.ID_CURSO = " + idCurso; // Filtrar pelo ID do curso
            // Removido JOIN com MATRIZ, pois HORA_ANO será substituído por CARGA_HORARIA da tabela DISCIPLINA

            // Usar o método CarregaDados da instância SQL
            dtMatrizes = SQL.CarregaDados(query);

            return dtMatrizes;
        }

    }
}
