﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ProjetoIntegrador.Professor.P_TelaPrincipal
{
    public partial class P_TelaPrincipal : Form
    {
        public P_TelaPrincipal()
        {
            InitializeComponent();
        }

        private void P_TelaPrincipal_Load(object sender, EventArgs e)
        {

        }

        private void AbrirTela(Form novaTela)
        {
            try
            {
                bool estaMaximizado = this.WindowState == FormWindowState.Maximized;

                // Oculta o formulário atual
                this.Hide();

                // Aplica o estado maximizado se necessário
                if (estaMaximizado)
                    novaTela.WindowState = FormWindowState.Maximized;

                // Configura o evento para mostrar este formulário quando o novo for fechado
                novaTela.FormClosed += (s, args) =>
                {
                    this.Show();
                    if (estaMaximizado)
                        this.WindowState = FormWindowState.Maximized;
                };

                // IMPORTANTE: Exibe o novo formulário
                novaTela.Show();
            }
            catch (Exception ex)
            {
                // Em caso de erro, garante que o formulário atual seja mostrado
                this.Show();
                MessageBox.Show($"Erro ao abrir formulário: {ex.Message}", "Erro",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_fechar_professor_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
             "Deseja realmente fechar a aplicação?",
             "Confirmação",
             MessageBoxButtons.YesNo,
             MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }

        private void btn_maximizar_professor_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_minimizar_professor_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        // Método para o botão de disponibilidade (pode ter nomes diferentes no Designer)
        private void btn_disponibilidade_Click(object sender, EventArgs e)
        {
            AbrirFormularioDisponibilidade();
        }

        private void btn_disponibilidade_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirFormularioDisponibilidade();
        }

        private void AbrirFormularioDisponibilidade()
        {
            try
            {
                AbrirTela(new ProjetoIntegrador.Professor.P_IDisponibilidade());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao abrir formulário de disponibilidade: {ex.Message}",
                              "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Método para o botão de visualizar disponibilidade (pode ter nomes diferentes no Designer)
        private void btn_visualizardisponibilidade_Click(object sender, EventArgs e)
        {
            AbrirFormularioVisualizarDisponibilidade();
        }

        private void btn_visualizardisponibilidade_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirFormularioVisualizarDisponibilidade();
        }

        private void AbrirFormularioVisualizarDisponibilidade()
        {
            try
            {
                // Corrigido: namespace correto é P_Disponibilidade, não P_Disponibilidade.P_VisualizarDisponibilidade
                AbrirTela(new ProjetoIntegrador.Professor.P_Disponibilidade.P_VisualizarDisponibilidade());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao abrir formulário de visualizar disponibilidade: {ex.Message}",
                              "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_conferirgrade_Click(object sender, EventArgs e)
        {
            // Implementar quando necessário
        }

        // Métodos alternativos usando ShowDialog() para maior estabilidade
        private void AbrirTelaModal(Form novaTela)
        {
            try
            {
                // Usa ShowDialog() que é mais estável para navegação
                DialogResult resultado = novaTela.ShowDialog();

                // O formulário atual permanece visível após fechar o modal
                // Não precisa de Hide() nem Show() pois ShowDialog() é bloqueante
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao abrir formulário: {ex.Message}", "Erro",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Métodos públicos para serem chamados externamente se necessário
        public void AbrirDisponibilidade()
        {
            AbrirFormularioDisponibilidade();
        }

        public void AbrirVisualizarDisponibilidade()
        {
            AbrirFormularioVisualizarDisponibilidade();
        }

        // Método alternativo usando ShowDialog() para disponibilidade
        public void AbrirDisponibilidadeModal()
        {
            try
            {
                using (var form = new ProjetoIntegrador.Professor.P_IDisponibilidade())
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao abrir formulário de disponibilidade: {ex.Message}",
                              "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Método alternativo usando ShowDialog() para visualizar disponibilidade
        public void AbrirVisualizarDisponibilidadeModal()
        {
            try
            {
                using (var form = new ProjetoIntegrador.Professor.P_Disponibilidade.P_VisualizarDisponibilidade())
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao abrir formulário de visualizar disponibilidade: {ex.Message}",
                              "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
